import { Heading, Text } from "@onyma-ds/react";
import styled, { css } from "styled-components";

export const Container = styled.div`
  background: linear-gradient(180deg, #dcf6f8 0%, #c0eff2 100%);
  width: 100%;
  padding: 6.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6.25rem;
  position: relative;

  & > h2 {
    color: ${({ theme }) => theme.colors.secondary};
    text-align: center;
    max-width: 500px;
    position: relative;
    z-index: 1;
  }

  @media (max-width: 1200px) {
    padding: 5rem;
  }

  @media (max-width: 920px) {
    padding: 4rem;
  }

  @media (max-width: 720px) {
    padding: 3rem;
  }

  @media (max-width: 490px) {
    padding: 2rem;
    gap: 0;
  }
`;

export const Content = styled.section`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 6.25rem;
  position: relative;
  z-index: 1;

  @media (max-width: 490px) {
    gap: 0;
  }
`;

export const FunctionalityImage = styled.div`
  width: 50%;
  position: relative;
  aspect-ratio: 1;
  max-width: 500px;
`;

export const FunctionalityExplanation = styled.div`
  width: 50%;
  padding: 0 ${({ theme }) => theme.spacings.xs};
  max-width: 379px;
  margin-top: ${({ theme }) => theme.spacings.xs};
`;

export const FunctionalityTitle = styled(Heading).attrs({
  as: "h3",
  type: "heading_03",
})`
  color: ${({ theme }) => theme.colors.secondary};
  margin-bottom: ${({ theme }) => theme.spacings.nano};
`;

export const FunctionalityDescription = styled(Text).attrs({
  type: "body_02",
})``;

type FuncionalityBoxProps = {
  leftItem: "image" | "text";
};

export const FunctionalityBox = styled.section<FuncionalityBoxProps>`
  width: 100%;
  display: flex;
  justify-content: center;

  ${({ leftItem }) =>
    leftItem === "text" &&
    css`
      ${FunctionalityImage} {
        order: 2;
      }

      ${FunctionalityExplanation} {
        text-align: end;
        justify-self: flex-end;
      }
    `};

  @media (max-width: 490px) {
    padding: 2rem;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;

    ${FunctionalityImage} {
      width: 100%;
      order: 1;
    }

    ${FunctionalityExplanation} {
      width: 100%;
      text-align: center;
      justify-self: center;
    }
  }
`;

export const HeartImageContourBoxCrop = styled.div`
  width: 95%;
  aspect-ratio: 1.67;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 0;
`;

export const HeartImageContourBox = styled.div`
  position: relative;
  object-fit: contain;
  width: 120%;
  margin-top: -12%;
  aspect-ratio: 1.67;
  opacity: 0.4;
  color: ${({ theme }) => theme.colors.secondary};
  display: flex;
`;

export const HeartImageBoxCrop = styled.div`
  width: 40%;
  overflow: hidden;
  direction: rtl;
  position: absolute;
  left: -5%;
  bottom: -3%;

  @media (max-width: 720px) {
    bottom: -2%;
  }

  @media (max-width: 490px) {
    bottom: -1%;
  }
`;

export const HeartImageBox = styled.div`
  position: relative;
  object-fit: cover;
  aspect-ratio: 1.67;
  opacity: 0.4;
  color: ${({ theme }) => theme.colors.black};
  mix-blend-mode: overlay;
`;
