/* eslint-disable react-hooks/exhaustive-deps */
import { forwardRef, useImperativeHandle, useRef } from "react";
import { OnyAILogoRef, Props } from "./types";

const OnyAILogo = forwardRef<OnyAILogoRef, Props>(function OnyAILogo(
  props,
  ref: any
) {
  const leftEyeAnimateRef = useRef<SVGAnimationElement>(null);
  const rightEyeAnimateRef = useRef<SVGAnimationElement>(null);

  const handleBlinkLeftEye = () => {
    leftEyeAnimateRef.current?.beginElement();
  };

  const handleBlinkRightEye = () => {
    rightEyeAnimateRef.current?.beginElement();
  };

  const handleBlinkAllEyes = () => {
    handleBlinkLeftEye();
    handleBlinkRightEye();
  };

  useImperativeHandle(
    ref,
    () => {
      return {
        onBlinkLeftEye: handleBlinkLeftEye,
        onBlinkRightEye: handleBlinkRightEye,
        onBlinkAllEyes: handleBlink<PERSON>llEyes,
      };
    },
    []
  );

  return (
    <svg
      viewBox="0 0 192 155"
      fill="#ffffff"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M95.9995 150.5C47.5257 150.5 4 146.224 4 94.5062C4 70.5367 13.3919 47.96 30.4589 30.9528C47.9297 13.5479 71.1569 4 95.9995 4C117.712 4 138.819 11.5587 155.381 25.3833C176.083 42.6888 188 67.8514 188 94.6053C188 146.224 144.474 150.5 95.9995 150.5Z"
        fill="#ffffff"
      />
      <path
        d="M95.9995 154.502C45.4182 154.502 0 149.992 0 95.4498C0 70.1711 9.80022 46.3612 27.6092 28.425C45.8397 10.0694 70.0768 0 95.9995 0C118.656 0 140.681 7.97161 157.963 22.5513C179.565 40.802 192 67.3391 192 95.5544C192 149.992 146.582 154.502 95.9995 154.502ZM95.9995 8.81071C72.5005 8.81071 50.4764 17.9361 34.0373 34.6135C17.9144 50.8714 8.9572 72.4787 8.9572 95.4498C8.9572 137.93 38.2525 145.691 95.9995 145.691C153.747 145.691 183.043 137.93 183.043 95.4498C183.043 69.8565 171.768 45.7318 152.167 29.2641C136.465 16.0481 116.549 8.81071 95.9995 8.81071Z"
        fill="#023D68"
      />
      <path
        id="right-eye"
        d="M144 63.9257C144 55.1541 136.812 48 128 48C119.188 48 112 55.1541 112 63.9257C112 72.6973 128 63.5525 128 63.5525C128 63.5525 144 72.6973 144 63.9257Z"
        fill="#27B5BF"
      >
        <animate
          ref={rightEyeAnimateRef}
          attributeName="d"
          values="M144 63.9257C144 55.1541 136.812 48 128 48C119.188 48 112 55.1541 112 63.9257C112 72.6973 128 63.5525 128 63.5525C128 63.5525 144 72.6973 144 63.9257Z; M144 59.049C144 56.8561 136.812 55.0675 128 55.0675C119.188 55.0675 112 56.8561 112 59.049C112 61.2419 128 58.9557 128 58.9557C128 58.9557 144 61.2419 144 59.049Z; M144 63.9257C144 55.1541 136.812 48 128 48C119.188 48 112 55.1541 112 63.9257C112 72.6973 128 63.5525 128 63.5525C128 63.5525 144 72.6973 144 63.9257Z"
          dur="1s"
          begin=""
          repeatCount="0"
          restart="whenNotActive"
        />
      </path>
      <path
        id="left-eye"
        d="M80 63.9257C80 55.1541 72.8125 48 64 48C55.1875 48 48 55.1541 48 63.9257C48 72.6973 64 63.5525 64 63.5525C64 63.5525 80 72.6973 80 63.9257Z"
        fill="#27B5BF"
      >
        <animate
          ref={leftEyeAnimateRef}
          attributeName="d"
          values="M80 63.9257C80 55.1541 72.8125 48 64 48C55.1875 48 48 55.1541 48 63.9257C48 72.6973 64 63.5525 64 63.5525C64 63.5525 80 72.6973 80 63.9257Z; M80 59.049C80 56.8561 72.8125 55.0675 64 55.0675C55.1875 55.0675 48 56.8561 48 59.049C48 61.2419 64 58.9557 64 58.9557C64 58.9557 80 61.2419 80 59.049Z; M80 63.9257C80 55.1541 72.8125 48 64 48C55.1875 48 48 55.1541 48 63.9257C48 72.6973 64 63.5525 64 63.5525C64 63.5525 80 72.6973 80 63.9257Z"
          dur="1s"
          begin=""
          repeatCount="0"
          restart="whenNotActive"
        />
      </path>
    </svg>
  );
});

export default OnyAILogo;
