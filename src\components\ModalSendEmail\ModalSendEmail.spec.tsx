import { fireEvent, screen, waitFor } from "@testing-library/dom";
import { renderTheme } from "@/utils/renderTheme";
import ModalSendEmail from ".";
import * as Helpers from "./components/Form/tests/utils";
import { ApiContext } from "@/contexts/ApiContext";

type SutParams = {
  show?: boolean;
};

type SutTypes = {
  onCloseMock: jest.Mock;
};

const makeSut = ({ show = true }: SutParams = {}): SutTypes => {
  const onCloseMock = jest.fn();
  renderTheme(
    <ApiContext.Provider
      value={{ remoteSendEmailToCommercialTeam: jest.fn() } as any}
    >
      <ModalSendEmail show={show} onClose={onCloseMock} />
    </ApiContext.Provider>
  );
  return {
    onCloseMock,
  };
};

describe("<ModalSendEmail />", () => {
  it("should show correctly", () => {
    makeSut();
    const modalElement = screen.queryByTestId("modal-send-email-test");
    expect(modalElement).toBeTruthy();
  });

  it("should not show if show is false", () => {
    makeSut({ show: false });
    const modalElement = screen.queryByTestId("modal-send-email-test");
    expect(modalElement).toBeFalsy();
  });

  it("should call onClose correctly", () => {
    const { onCloseMock } = makeSut();
    const closeButton = screen.getByTitle("Fechar");
    fireEvent.click(closeButton);
    expect(onCloseMock).toHaveBeenCalled();
  });

  it("should hide form and show confirmation correctly", async () => {
    makeSut();
    Helpers.prefillForm();
    Helpers.submitForm();
    await waitFor(() => {
      expect(screen.queryByText("Mensagem enviada")).toBeTruthy();
    });
  });
});
