import { useReducer, useState } from "react";

import { formReducer } from "./formReducer";
import { initialFormState } from "./data";
import { FormValidation, validateForm } from "../../validation";

export default function useFormState() {
  const [formState, formDispatch] = useReducer(formReducer, initialFormState);
  const [errors, setErrors] = useState<FormValidation>({});

  const handleValidate = async () => {
    const errors = await validateForm(formState);
    if (!errors) return null;
    setErrors(errors);
    return errors;
  };

  return {
    formState,
    formDispatch,
    errors,
    validate: handleValidate,
  };
}
