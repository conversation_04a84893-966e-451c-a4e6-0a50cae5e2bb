import { useCallback, useEffect, useState } from "react";
import { socketOnyAI } from "@/lib/socket";

import { useUser } from "../../../../contexts";
import { Message } from "../../types";

export default function useMessages() {
  const { token, questionsLeft, setQuestionsLeft } = useUser();

  const [messages, setMessages] = useState<Message[]>([]);

  const formatMessage = useCallback(
    (message: string, author: Message["author"]) => {
      let id = "1";
      const lastMessage = messages.at(-1);
      if (lastMessage) {
        id = (Number(lastMessage.id) + 1).toString();
      }
      const newMessage: Message = {
        id,
        author,
        text: message,
      };
      return newMessage;
    },
    [messages]
  );

  const sendQuestion = (question: string) => {
    const newUserMessage: Message = formatMessage(question, "user");
    setMessages((prevMessages) => [...prevMessages, newUserMessage]);
    socketOnyAI.emit("question", {
      token: token || "",
      question,
    });
  };

  useEffect(() => {
    socketOnyAI.on("answer", (data) => {
      const newOnyAIMessage: Message = formatMessage(
        data.conversation || data.error,
        "onyai"
      );
      setMessages((prevMessages) => [...prevMessages, newOnyAIMessage]);
      setQuestionsLeft(data.questionsLeft || 0);
    });
    return () => {
      socketOnyAI.off("answer");
    };
  }, [formatMessage]);

  useEffect(() => {
    if (!socketOnyAI.connected) {
      socketOnyAI.connect();
    }
    return () => {
      socketOnyAI.disconnect();
    };
  }, []);

  return {
    messages,
    questionsLeft,
    sendQuestion,
  };
}
