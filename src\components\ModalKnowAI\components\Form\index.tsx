import { useCallback, useEffect, useState } from "react";
import Link from "next/link";
import { Symbol, useToast } from "@onyma-ds/react";

import { Spinner } from "@/components";
import { InputTextBox } from "./components";
import { useApi } from "@/contexts/ApiContext";
import { FormState, useForm } from "../../contexts/hooks";
import { useUser } from "../../contexts";

import { FormProps } from "./types";
import styles from "./styles.module.scss";

export default function Form({ onContinue }: FormProps) {
  const { onyAI, remoteSendEmailToCommercialTeam } = useApi();
  const { addToast } = useToast();
  const { setToken, setQuestionsLeft } = useUser();
  const { formState, formDispatch, errors, onSubmit } = useForm();

  const [loadingSubmitForm, setLoadingSubmitForm] = useState(false);

  const handleInputTextChange = useCallback(
    (name: string, value: any) => {
      formDispatch({
        type: "CHANGE_INPUT_TEXT",
        payload: { field: name as keyof FormState, value },
      });
    },
    [formDispatch]
  );

  const handleSignUp = async () => {
    try {
      const result = await onyAI.signUp({
        email: formState.email,
        name: formState.name,
        phone: formState.phone,
        lives: formState.numberOfEmployees,
      });
      return result;
    } catch (error) {
      const [, message] = (error.message || "").split(":");
      addToast({
        type: "error",
        title: "Cadastro",
        description: message || error.message,
        timeout: 5000,
      });
    }
  };

  const handleSendLeadEmail = async () => {
    try {
      await remoteSendEmailToCommercialTeam({
        form: {
          name: formState.name,
          role: "-",
          email: formState.email,
          phone: formState.phone,
          cnpj: "-",
          numberOfCollaborators: formState.numberOfEmployees,
          averageMonthlyAdmissions: 0,
          averageMonthlyLayoffs: 0,
          workModel: "-",
          servicesOfInterest: [],
          message:
            "Acabei de me cadastrar na OnyAI e gostaria de saber mais sobre os serviços da Onyma.",
        },
      });
    } catch (error) {
      console.error(error);
    }
  };

  const handleSubmitForm = async () => {
    try {
      setLoadingSubmitForm(true);
      if (!formState.showComplementaryFields) {
        onyAI
          .signIn({
            email: formState.email,
          })
          .then((result) => {
            sessionStorage.setItem("OnyAI/email", formState.email);
            setToken(result.token);
            setQuestionsLeft(result.questionsLeft);
            return onContinue();
          })
          .catch((error) => {
            if (error.statusCode === 401) {
              return handleInputTextChange("showComplementaryFields", true);
            }
            const [, message] = (error.message || "").split(":");
            return addToast({
              type: "error",
              title: "Erro",
              description: message || error.message,
              timeout: 5000,
            });
          })
          .finally(() => setLoadingSubmitForm(false));
        return;
      }

      const signUpResult = await handleSignUp();
      if (signUpResult && signUpResult.token) {
        await handleSendLeadEmail();
        setLoadingSubmitForm(false);
        sessionStorage.setItem("OnyAI/email", formState.email);
        setToken(signUpResult.token);
        setQuestionsLeft(signUpResult.questionsLeft);
        return onContinue();
      }
      throw new Error(
        "Não foi possível realizar o cadastro para acessar a OnyAI. Tente novamente mais tarde!"
      );
    } catch (error) {
      setLoadingSubmitForm(false);
    }
  };

  useEffect(() => {
    const emailCookie = sessionStorage.getItem("OnyAI/email");
    if (emailCookie) handleInputTextChange("email", emailCookie);
  }, [handleInputTextChange]);

  const buttonMessage = formState.showComplementaryFields ? (
    <>
      Bora usar a Ony.AI agora!
      <Symbol name="chevron_right" size={22} />
    </>
  ) : (
    <>
      Continuar
      <Symbol name="chevron_right" size={22} />
    </>
  );

  return (
    <form id={styles.companyInfo} onSubmit={onSubmit(handleSubmitForm)}>
      <section className={styles.modalHeader}>
        <h3>
          A OnyAI é um chat baseado em Inteligência Artificial para você tirar
          todas as suas dúvidas sobre SST
        </h3>
        <h4>Preencha o campo abaixo para continuar</h4>
      </section>
      <section className={styles.modalBody}>
        <div
          className={`${
            formState.showComplementaryFields
              ? styles.gridFields
              : styles.centeredField
          }`}
        >
          <InputTextBox
            type="email"
            id="company-email"
            name="email"
            label="Digite seu e-mail"
            placeholder="<EMAIL>"
            value={formState.email}
            onChange={({ target: { name, value } }) =>
              handleInputTextChange(name, value)
            }
            error={errors.email?.error}
          />

          {formState.showComplementaryFields && (
            <>
              <InputTextBox
                id="company-name"
                name="name"
                label="Seu nome"
                placeholder="Digite o seu nome"
                value={formState.name}
                onChange={({ target: { name, value } }) =>
                  handleInputTextChange(name, value)
                }
                error={errors.name?.error}
              />
              <InputTextBox
                type="tel"
                id="company-phone"
                name="phone"
                label="Seu telefone"
                placeholder="(00) 00000-0000"
                value={formState.phone}
                onChange={({ target: { name, value } }) =>
                  handleInputTextChange(name, value)
                }
                error={errors.phone?.error}
              />
              <InputTextBox
                id="number-of-employees"
                name="numberOfEmployees"
                label="Número de colaboradores"
                placeholder="1"
                inputMode="numeric"
                min={1}
                value={formState.numberOfEmployees}
                onChange={({ target: { name, value } }) =>
                  handleInputTextChange(name, value)
                }
                error={errors.numberOfEmployees?.error}
              />
            </>
          )}
        </div>
      </section>
      <section className={styles.modalFooter}>
        {formState.showComplementaryFields && (
          <>
            <p className={styles.termsOfUse}>
              Ao clicar no botão abaixo, você aceita os{" "}
              <Link href="/termos-de-uso" target="_blank">
                termos de uso
              </Link>{" "}
              e{" "}
              <Link
                href="/documents/politica-de-privacidade.pdf"
                target="_blank"
              >
                política de privacidade
              </Link>
              .
            </p>
            <p className={styles.onyAIWarning}>
              *A OnyAI é alimentada por inteligência artificial, portanto,
              respostas imprecisas e/ou com erro podem acontecer. Certifique-se
              de checar todas as respostas e compartilhe seu feedback conosco
              para que possamos aprimorar o serviço
            </p>
          </>
        )}
        <button type="submit" disabled={loadingSubmitForm}>
          {loadingSubmitForm && (
            <Spinner id="spinner-modal-know-ai" size={22} />
          )}
          {!loadingSubmitForm && buttonMessage}
        </button>
      </section>
    </form>
  );
}
