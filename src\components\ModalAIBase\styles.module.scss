.modalContainer {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 2;
  height: 100%;
  width: 100%;
  filter: invert(0);

  /* Works on Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: transparent gray;
  }
  /* Works on Chrome, Edge, and Safari */
  *::-webkit-scrollbar {
    width: 5px;
    height: 5px;

    &:hover {
      width: 8px;
      height: 8px;
    }
  }
  *::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }
  *::-webkit-scrollbar-thumb {
    background-color: gray;
    border-radius: 20px;
    border: 2px solid gray;

    &:hover {
      border-width: 5px;
    }
  }
}

@keyframes backStars {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.5);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

@keyframes showOverlay {
  0% {
    background-color: rgba(0, 0, 0, 0);
  }
}

.overlay {
  z-index: 3;
  position: fixed;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.99);
  animation: showOverlay 0.3s ease-in-out;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    animation: backStars 10s infinite linear;
    background-size: cover;
    background-image: url("./assets/bg-stars.svg");
  }
}

@keyframes fadeIn {
  from {
    top: 47%;
    opacity: 0;
  }
  to {
    opacity: 1;
    top: 50%;
  }
}

.modalContent {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0.3) 0%,
      rgba(0, 0, 0, 0.8) 100%
    ),
    url("./assets/background-image.png");
  background-size: cover;
  color: #fff;
  border-radius: 8px;
  z-index: 4;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.5s;
  animation: fadeIn 0.3s ease-out;

  & > button {
    background-color: transparent;
    border-color: transparent;
    color: white;
    position: absolute;
    right: 1.5rem;
    top: 1.5rem;
    z-index: 1;

    @media (max-width: 720px) {
      right: 0.75rem;
      top: 0.75rem;
    }
  }
}
