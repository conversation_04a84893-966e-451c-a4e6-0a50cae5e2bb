import { Heading, Text } from "@onyma-ds/react";
import { useRef, useState } from "react";
import * as SC from "./styles";
import { QuestionAccordionProps } from "./types";

export default function QuestionAccordion({
  title,
  answer,
}: QuestionAccordionProps) {
  const contentBoxRef = useRef<HTMLDivElement>(null);

  const [visible, setVisible] = useState(false);

  const handleChangeVisibility = () => setVisible((prevState) => !prevState);

  return (
    <SC.Container id={`question-${title}`}>
      <SC.MainBox>
        <Heading as="h5" type="heading_05">
          {title}
        </Heading>
        <SC.ButtonShow
          title="Visualizar resposta"
          onClick={handleChangeVisibility}
        >
          <SC.AddSymbol aria-hidden={!visible} title="Visualizar resposta" />
          <SC.RemoveSymbol aria-hidden={visible} title="Esconder resposta" />
        </SC.ButtonShow>
      </SC.MainBox>
      <SC.ContentBox ref={contentBoxRef} aria-expanded={visible}>
        <Text as="article" type="body_03">
          {answer}
        </Text>
      </SC.ContentBox>
    </SC.Container>
  );
}
