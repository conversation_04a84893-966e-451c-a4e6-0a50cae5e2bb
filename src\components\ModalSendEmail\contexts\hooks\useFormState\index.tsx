import { useReducer, useState } from "react";
import { FormValidation, validateForm } from "../../validation";
import { initialFormState } from "./data";
import { formReducer } from "./formReducer";

export default function useFormState() {
  const [formState, formDispatch] = useReducer(formReducer, initialFormState);
  const [errors, setErrors] = useState<FormValidation>({});

  const handleValidate = async () => {
    const errors = await validateForm(formState);
    if (!errors) return null;
    setErrors(errors);
    return errors;
  };

  return {
    formState,
    errors,
    formDispatch,
    validate: handleValidate,
  };
}
