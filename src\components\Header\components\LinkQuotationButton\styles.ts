import { Button } from "@onyma-ds/react";
import styled from "styled-components";

export const Container = styled.div`
  display: flex;
  gap: 1rem;
`;

export const ButtonQuote = styled(Button).attrs({
  type: "button",
  variant: "secondary",
})`
  color: ${({ theme }) => theme.colors.white};
  white-space: nowrap;

  & > a {
    all: unset;
  }
`;

export const WhatsAppButton = styled(Button).attrs({
  type: "button",
})`
  padding: 0.5rem;
  aspect-ratio: 1;
  background-color: #25d366;
  border-color: #25d366;
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const ImageBox = styled.div`
  position: relative;
  width: 1.75rem;
  aspect-ratio: 1;
`;
