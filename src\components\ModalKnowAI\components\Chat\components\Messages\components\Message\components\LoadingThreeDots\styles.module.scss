.container {
  width: 100%;
  height: 100%;
  padding: 0.25rem 0;

  display: flex;
  gap: 0.25rem;
}

@keyframes loading {
  0% {
    top: -5px;
    filter: brightness(1);
    animation-timing-function: ease-in;
  }
  34% {
    transform: scale(1, 1);
  }
  35% {
    top: 5px;
    transform: scale(1.3, 0.7);
    filter: brightness(0.5);
    animation-timing-function: ease-out;
  }
  45% {
    transform: scale(1, 1);
  }
  90% {
    top: -5px;
    filter: brightness(1);
  }
  100% {
    top: -5px;
    filter: brightness(1);
  }
}

.dot {
  width: 8px;
  height: 8px;
  background-color: #ccc;
  border-radius: 50%;

  position: relative;
  animation: 800ms linear infinite loading;

  &:nth-child(1) {
    animation-delay: 0;
  }

  &:nth-child(2) {
    animation-delay: 100ms;
  }

  &:nth-child(3) {
    animation-delay: 200ms;
  }
}
