import styled, { css } from "styled-components";
import { Button, Symbol } from "@onyma-ds/react";

export const Background = styled.div`
  @media (max-width: 980px) {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    background: ${({ theme }) => theme.colors.black};
    filter: opacity(0.5);
  }
`;

export const Container = styled.header`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: ${({ theme }) => theme.spacings.lg};

  position: relative;
  z-index: 2;

  @media (max-width: 428px) {
    gap: ${({ theme }) => theme.spacings.xs};
  }

  ${({ theme }) => css`
    background-color: ${theme.colors.white};
    padding: ${theme.spacings.xs} 0;

    @media (max-width: 980px) {
      justify-content: space-between;
      padding: ${theme.spacings.xs} ${theme.spacings.lg};
      padding-top: calc(${theme.spacings.xs} + ${theme.spacings.sm});
    }

    @media (max-width: 428px) {
      padding: ${theme.spacings.xs} ${theme.spacings.xxxs};
      padding-top: calc(${theme.spacings.xs} + ${theme.spacings.sm});
    }
  `};
`;

export const ButtonMenu = styled.button`
  background-color: transparent;
  border: none;
  display: none;
  cursor: pointer;
`;

export const MenuSymbol = styled(Symbol).attrs({
  name: "menu",
  size: 32,
})`
  color: ${({ theme }) => theme.colors.secondary};
`;

export const CloseSymbol = styled(Symbol).attrs({
  name: "close",
  size: 32,
})`
  color: ${({ theme }) => theme.colors.secondary};
`;

export const Navbar = styled.section`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: ${({ theme }) => theme.spacings.sm};

  @media (max-width: 1390px) {
    gap: ${({ theme }) => theme.spacings.xs};
  }

  @media (max-width: 1220px) {
    gap: ${({ theme }) => theme.spacings.xxs};
  }

  @media (max-width: 1080px) {
    gap: ${({ theme }) => theme.spacings.xxxs};
  }

  @media (max-width: 980px) {
    & > a {
      display: none;
    }

    ${ButtonMenu} {
      display: block;
    }
  }

  & > a {
    color: ${({ theme }) => theme.colors.gray_40};
  }
`;

export const LinkButtons = styled.div`
  @media (max-width: 980px) {
    display: none;
  }
`;
