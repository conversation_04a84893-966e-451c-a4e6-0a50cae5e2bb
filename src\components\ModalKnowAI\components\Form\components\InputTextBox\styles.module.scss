.inputTextBox {
  display: flex;
  flex-direction: column;
  gap: 4px;

  & > label {
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 140%;
  }

  & > input {
    padding: 0.75rem;
    color: #333333;
    border: 1px solid #cccccc;
    border-radius: 4px;
    background-color: #ffffff;

    &::placeholder {
      color: #cccccc;
    }

    @media (max-width: 720px) {
      font-size: 0.875rem;
    }
  }

  & > p {
    font-size: 0.75rem;
    font-weight: 400;
    line-height: 140%;
    color: #ff3b30;
  }
}
