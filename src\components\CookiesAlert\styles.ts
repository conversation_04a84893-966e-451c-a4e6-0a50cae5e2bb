import { Button, Heading, Text, Switch } from "@onyma-ds/react";
import styled from "styled-components";

export const Container = styled.div`
  width: 100vw;
  max-width: 384px;
  background-color: ${({ theme }) => theme.colors.white};
  box-shadow: 0px 0px 32px rgba(0, 0, 0, 0.16);
  padding: 2rem;
  border-radius: 8px;
  position: fixed;
  right: 4rem;
  bottom: 4rem;
  z-index: 999;

  @media (max-width: 480px) {
    right: 50%;
    bottom: 2rem;
    transform: translateX(50%);
  }

  @media (max-width: 384px) {
    width: 100vw;
    max-width: max-content;
    bottom: 0;
  }
`;

export const Title = styled(Heading)`
  color: ${({ theme }) => theme.colors.black};
  margin-bottom: 8px;
`;

export const Excerpt = styled(Text)`
  color: ${({ theme }) => theme.colors.gray_40};
  margin-bottom: 1rem;

  & > a {
    color: ${({ theme }) => theme.colors.tertiary};
  }
`;

export const CookiesManager = styled.div`
  font-size: 1.25rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  label {
    font-size: 1rem;
  }
`;

export const ActionButtons = styled.div`
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

export const MainButton = styled(Button).attrs({
  type: "button",
})`
  color: ${({ theme }) => theme.colors.white};
`;

export const SecondaryButton = styled(Button).attrs({
  type: "button",
})``;
