import { useEffect, useRef, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { Symbol } from "@onyma-ds/react";
import { ModalSendEmail } from "@/components";
import {
  MessageCounter,
  Messages,
  OnyAILogo,
  OnyAILogoRef,
  PromptSuggestions,
} from "./components";
import { useMessages } from "./hooks";
import { svgsPaths } from "@/assets/paths/svgPaths";
import styles from "./styles.module.scss";
import { generators } from "@/utils/generators";

export default function Chat() {
  const onyAiLogoRef = useRef<OnyAILogoRef>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const { messages, questionsLeft, sendQuestion } = useMessages();

  const [glowOpacity, setGlowOpacity] = useState<"None" | "Full" | "Half">(
    "Half"
  );
  const [rotate, setRotate] = useState(false);
  const [textToSend, setTextToSend] = useState("");
  const [showModalSendEmail, setShowModalSendEmail] = useState(false);

  const inputDisabled = messages.at(-1)?.author === "user";

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setGlowOpacity("Full");
    setTextToSend(event.target.value);
  };

  const handleSendMessage = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!textToSend) return;
    sendQuestion(textToSend);
    setRotate(true);
    setTextToSend("");
    inputRef.current?.blur();
  };

  const handleSendSuggestion = (suggestion: string) => {
    setRotate(true);
    if (!inputDisabled) sendQuestion(suggestion);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      onyAiLogoRef.current?.onBlinkAllEyes();
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setGlowOpacity("Half");
    }, 2000);
    return () => clearTimeout(timeout);
  }, [textToSend]);

  useEffect(() => {
    if (rotate) {
      const timeout = setTimeout(() => {
        setRotate(false);
      }, 1000);
      return () => clearTimeout(timeout);
    }
  }, [rotate]);

  useEffect(() => {
    const body = document.querySelector("body");
    if (body) {
      body.style.backgroundColor = "#000";
      return () => {
        body.style.backgroundColor = "#fff";
      };
    }
  }, []);

  return (
    <>
      <meta
        name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, viewport-fit=cover"
      ></meta>
      <div className={styles.container}>
        <main className={styles.chatMain}>
          <header className={styles.chatHeader}>
            <div>
              <Image
                src="/modules/onyAI/ony-logo-contour.svg"
                alt=""
                width={35}
                height={28}
              />
              <h3>OnyAI</h3>
            </div>
          </header>
          <section className={styles.chatInterface}>
            <div className={styles.messagesAndCounterWrapper}>
              <section className={styles.callButtons}>
                <Link
                  href="https://seja.onyma.digital/formulario"
                  target="_blank"
                  rel="noreferrer"
                  className={[styles.actionButton, styles.buttonBeOnyma].join(
                    " "
                  )}
                  title="Acessar nossa Landing Page"
                >
                  ✨ Seja Onyma
                </Link>
                <button
                  type="button"
                  className={[styles.actionButton, styles.emailButton].join(
                    " "
                  )}
                  title="Enviar uma mensagem por E-mail"
                  onClick={() => setShowModalSendEmail(true)}
                >
                  <Symbol name="mail" />
                  <span>Formulário de contato</span>
                </button>
                <Link
                  href={generators.whatsAppLink()}
                  target="_blank"
                  rel="noreferrer"
                  className={[styles.actionButton, styles.whatsappButton].join(
                    " "
                  )}
                  title="Falar com a equipe de vendas"
                >
                  <Image
                    src={svgsPaths.socialMedia.whatsAppWhite.path}
                    alt="Logo do WhatsApp"
                    width={16}
                    height={16}
                  />
                  <span>Enviar um WhatsApp</span>
                </Link>
                <MessageCounter questionsLeft={questionsLeft} />
              </section>
              <Messages messages={messages} />
            </div>
            <PromptSuggestions onSuggestionClick={handleSendSuggestion} />
            <form
              className={styles.chatInputBox}
              noValidate
              onSubmit={handleSendMessage}
            >
              <input
                ref={inputRef}
                type="text"
                placeholder="Envie sua mensagem"
                value={textToSend}
                onChange={handleInputChange}
                disabled={inputDisabled}
              />
              <button type="submit" disabled={inputDisabled}>
                <Symbol name="send" size={20} />
              </button>
            </form>
          </section>
          <section id="chatSection" className={styles.chatImage}>
            <div
              className={[
                styles.logoBox,
                styles.logoBoxGlow,
                styles[`op${glowOpacity}`],
              ].join(" ")}
            >
              <p className={styles.chatBaloon}>
                Olá! Eu sou a Ony.
                <br />
                Vamos conversar?
              </p>
              <OnyAILogo
                ref={onyAiLogoRef}
                className={rotate ? styles.rotate : ""}
              />
            </div>
          </section>
        </main>
      </div>

      {showModalSendEmail && (
        <ModalSendEmail
          show={showModalSendEmail}
          onClose={() => setShowModalSendEmail(false)}
        />
      )}
    </>
  );
}
