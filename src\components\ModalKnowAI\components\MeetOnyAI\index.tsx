import Image from "next/image";

import { Symbol } from "@onyma-ds/react";
import { svgsPaths } from "@/assets/paths/svgPaths";

import { MeetOnyAIProps } from "./types";
import styles from "./styles.module.scss";

export default function MeetOnyAI({ onContinue }: MeetOnyAIProps) {
  return (
    <main className={styles.container}>
      <section className={styles.modalHeader}>
        <h2>Novidade!</h2>
        <h1>
          Conheça a{" "}
          <span>
            <span>Ony.AI</span>{" "}
            <Image src={svgsPaths.ai.contourOnyAi.path} fill alt="image" />
          </span>
        </h1>
      </section>
      <section className={styles.modalBody}>
        <p>
          A primeira inteligência artificial com foco e contexto em Saúde e
          Segurança do Trabalho do Brasil!
        </p>
      </section>
      <section className={styles.modalFooter}>
        <button type="button" onClick={onContinue}>
          Quero experimentar agora! <Symbol name="chevron_right" size={22} />
        </button>
      </section>
    </main>
  );
}
