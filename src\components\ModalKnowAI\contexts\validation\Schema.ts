import * as yup from "yup";

const requiredField = "Campo obrigatório";
const invalidEmail = "E-mail inválido";
const invalidPhone = "Telefone inválido";
const positiveNumber = "O número de colaboradores tem que ser maior que zero";

export const formSchema = yup.object({
  email: yup.string().email(invalidEmail).required(requiredField),
  showComplementaryFields: yup.boolean(),
  numberOfEmployees: yup.number().when("showComplementaryFields", {
    is: true,
    then: (schema) => schema.positive(positiveNumber).required(requiredField),
  }),
  name: yup.string().when("showComplementaryFields", {
    is: true,
    then: (schema) => schema.required(requiredField),
  }),
  phone: yup.string().when("showComplementaryFields", {
    is: true,
    then: (schema) =>
      schema
        .min(14, invalidPhone)
        .max(15, invalidPhone)
        .matches(/^\(\d{2}\)\s\d{4,5}-\d{4}$/, invalidPhone)
        .required(requiredField),
  }),
});
