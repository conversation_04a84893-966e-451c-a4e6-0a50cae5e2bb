import Image from "next/image";
import { Heading } from "@onyma-ds/react";

import { pngPaths } from "@/assets/paths/pngPaths";
import OnymaHeart from "@/../public/svgs/onyma/onyma-heart-current-color.svg";
import OnymaHeartContour from "@/../public/svgs/onyma/onyma-heart-contour-current-color.svg";
import * as SC from "./styles";

export default function Functionalities() {
  return (
    <SC.Container id="o-que-fazemos">
      <SC.HeartImageContourBoxCrop>
        <SC.HeartImageContourBox>
          <OnymaHeartContour />
        </SC.HeartImageContourBox>
      </SC.HeartImageContourBoxCrop>
      <Heading id="section-title" as="h2" type="heading_01">
        Tudo o que a Onyma pode fazer pela sua empresa
      </Heading>
      <SC.Content>
        <SC.FunctionalityBox leftItem="image">
          <SC.FunctionalityImage>
            <Image
              src={pngPaths.onyma.newProcessButtons.path}
              fill
              loading="lazy"
              alt="Ilustração dos processos que podem ser criado pelo RH"
            />
          </SC.FunctionalityImage>
          <SC.FunctionalityExplanation>
            <SC.FunctionalityTitle>
              Tecnologia descomplicada
            </SC.FunctionalityTitle>
            <SC.FunctionalityDescription>
              Nossa plataforma foi desenvolvida com o objetivo de ser intuitiva
              e de fácil utilização, e continuamos aprimorando-a constantemente
              para garantir a melhor experiência possível para nossos usuários
            </SC.FunctionalityDescription>
          </SC.FunctionalityExplanation>
        </SC.FunctionalityBox>
        <SC.FunctionalityBox leftItem="text">
          <SC.FunctionalityImage>
            <Image
              src={pngPaths.onyma.exampleWhatsappMessageNewAppointment.path}
              fill
              loading="lazy"
              alt="Ilustração da interação da Onyma com os pacientes pelo WhatsApp"
            />
          </SC.FunctionalityImage>
          <SC.FunctionalityExplanation>
            <SC.FunctionalityTitle>
              A melhor experiência de agendamento
            </SC.FunctionalityTitle>
            <SC.FunctionalityDescription>
              Com a plataforma Onyma, você pode solicitar seus ASOs
              admissionais, periódicos, demissionais e outros de forma simples e
              rápida. Deixe a Onyma cuidar de tudo para você, sem precisar se
              preocupar com burocracias ou complexidades. Economize tempo e
              garanta a qualidade dos seus processos de Saúde e Segurança do
              Trabalho com eficiência
            </SC.FunctionalityDescription>
          </SC.FunctionalityExplanation>
        </SC.FunctionalityBox>
        <SC.FunctionalityBox leftItem="image">
          <SC.FunctionalityImage>
            <Image
              src={pngPaths.onyma.registerEmployeeImage.path}
              fill
              loading="lazy"
              alt="Ilustração da parte do fluxo de cadastro de nova vida relacionando o Setor/Cargo com o PCMSO"
            />
          </SC.FunctionalityImage>
          <SC.FunctionalityExplanation>
            <SC.FunctionalityTitle>
              Gestão eficiente de PGR e PCMSO
            </SC.FunctionalityTitle>
            <SC.FunctionalityDescription>
              A equipe da Onyma é altamente capacitada para garantir a
              elaboração dos documentos obrigatórios para a gestão da saúde e
              segurança dos colaboradores no ambiente de trabalho. Com sua
              expertise e tecnologia, a Onyma está pronta para atender às
              necessidades das empresas
            </SC.FunctionalityDescription>
          </SC.FunctionalityExplanation>
        </SC.FunctionalityBox>
        <SC.FunctionalityBox leftItem="text">
          <SC.FunctionalityImage>
            <Image
              src={pngPaths.onyma.eSocialEventList.path}
              fill
              loading="lazy"
              alt="Listagem dos eventos enviados para o eSocial com seu respective status"
            />
          </SC.FunctionalityImage>
          <SC.FunctionalityExplanation>
            <SC.FunctionalityTitle>
              Envio de eventos para o eSocial
            </SC.FunctionalityTitle>
            <SC.FunctionalityDescription>
              O eSocial é uma iniciativa do Governo Federal que facilita o
              registro de informações sobre os colaboradores de uma empresa. A
              Onyma oferece a integração dos eventos relacionados à saúde e
              segurança no trabalho diretamente na nossa plataforma, tornando o
              processo mais fácil e eficiente para você. Deixe que a Onyma cuide
              de tudo!
            </SC.FunctionalityDescription>
          </SC.FunctionalityExplanation>
        </SC.FunctionalityBox>
      </SC.Content>
      <SC.HeartImageBoxCrop>
        <SC.HeartImageBox>
          <OnymaHeart />
        </SC.HeartImageBox>
      </SC.HeartImageBoxCrop>
    </SC.Container>
  );
}
