import RabbitMQProvider from "@/providers/MessageBrokerProvider/RabbitMQProvider";
import { Input, WorkModel, ServicesOfInterest } from "./types";

export const sendEmailToCommercialTeamUseCase = async ({
  recipient,
  variables,
}: Input): Promise<void> => {
  const servicesOfInterestMapped = variables.servicesOfInterest
    .map((service) => {
      if (service in ServicesOfInterest)
        return ServicesOfInterest[service as keyof typeof ServicesOfInterest];
      return "";
    })
    .join(", ");

  const workModelMapped =
    variables.workModel === "-"
      ? ""
      : variables.workModel in WorkModel
      ? WorkModel[variables.workModel]
      : "";

  const rabbitMQProvider = RabbitMQProvider.getInstance();
  await rabbitMQProvider.connect();
  const recipients = typeof recipient === "string" ? [recipient] : recipient;
  const sendEmailPromises = recipients.map((recipientEmail) =>
    rabbitMQProvider.publish("messaging", "send", {
      header: {
        provider: "mailgun",
      },
      body: {
        email: recipientEmail,
        template: "contato-website",
        subject: process.env.SEND_EMAIL_SUBJECT || "Onyma Website - Lead",
        variables: {
          name: variables.name,
          role: variables.role,
          email: variables.email,
          phone: variables.phone,
          CNPJ: variables.cnpj,
          numberOfCollaborators: variables.numberOfCollaborators,
          averageMonthlyAdmissions: variables.averageMonthlyAdmissions,
          averageMonthlyLayoffs: variables.averageMonthlyLayoffs,
          servicesOfInterest: servicesOfInterestMapped,
          workModel: workModelMapped,
          message: variables.message,
        },
      },
      metadata: {
        requester: "front-onyma-website",
      },
    })
  );
  await Promise.all(sendEmailPromises);
};
