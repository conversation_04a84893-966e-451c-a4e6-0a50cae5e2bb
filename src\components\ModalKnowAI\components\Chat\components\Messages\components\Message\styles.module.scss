$breakpoint: 540px;

.container {
  height: fit-content;
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 0.5rem;
  max-width: 80%;

  &:not(:last-child) {
    margin-bottom: 0.75rem;
  }

  @media (max-width: $breakpoint) or (max-height: $breakpoint) {
    max-width: 90%;

    &:not(:last-child) {
      margin-bottom: 0.5rem;
    }
  }
}

.profilePicture {
  display: grid;
  place-items: center;

  border-radius: 4px;
  padding: 0.25rem;
  height: min-content;
  width: 28px;
  aspect-ratio: 1/1;

  font-size: 0.875rem;
  font-weight: 400;
  line-height: 140%;
}

.baloon {
  min-height: 28px;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;

  font-size: 0.875rem;
  font-weight: 400;
  line-height: 140%;

  & > pre {
    font-family: inherit;
    white-space: pre-wrap;
    white-space: -moz-pre-wrap;
    white-space: -pre-wrap;
    white-space: -o-pre-wrap;
    word-wrap: break-word;
  }

  @media (max-width: $breakpoint) or (max-height: $breakpoint) {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

.user {
  justify-self: flex-end;

  .baloon {
    background-color: #7159eb;
    color: #fff;
    position: relative;
  }

  .profilePicture {
    background-color: #2c14a8;
    color: #d9d2fa;
    order: 2;
  }
}

.bot {
  justify-self: flex-start;

  .baloon {
    background-color: #023d68;
    color: #ffffff;
  }

  .profilePicture {
    background-color: #023d68;
  }
}
