import { NextSeoProps } from "next-seo";

export const seoConfig: NextSeoProps = {
  title: "Onyma Digital",
  openGraph: {
    url: `${process.env.NEXT_PUBLIC_WEBSITE_URL}/`,
    article: {
      tags: [
        "onyma",
        "emissão de ASOs",
        "eSocial",
        "ocupacional",
        "saúde ocupacional",
        "admissionais",
        "periódicos",
        "demissionais",
        "PGR",
        "PCMSO",
      ],
    },
    images: [
      {
        url: `${process.env.NEXT_PUBLIC_WEBSITE_URL}/_next/image?url=%2Fpngs%2Fonyma%2Fdashboard-rh-image.png&w=1920&q=100`,
        width: 982,
        height: 545.56,
        alt: "Dashboard RH da Onyma Digital",
      },
      {
        url: `${process.env.NEXT_PUBLIC_WEBSITE_URL}/_next/image?url=%2Fpngs%2Fonyma%2Fnew-process-buttons.png&w=1920&q=75`,
        width: 479,
        height: 479,
        alt: "Ilustração dos processos que podem ser criado pelo RH",
      },
      {
        url: `${process.env.NEXT_PUBLIC_WEBSITE_URL}/_next/image?url=%2Fpngs%2Fonyma%2Fexample-whatsapp-message-new-appointment.png&w=1920&q=75`,
        width: 479,
        height: 479,
        alt: "Ilustração da interação da Onyma com os pacientes pelo WhatsApp",
      },
      {
        url: `${process.env.NEXT_PUBLIC_WEBSITE_URL}/_next/image?url=%2Fpngs%2Fonyma%2Fregister-employee-image.png&w=1920&q=75`,
        width: 479,
        height: 479,
        alt: "Ilustração da parte do fluxo de cadastro de nova vida relacionando o Setor/Cargo com o PCMSO",
      },
      {
        url: `${process.env.NEXT_PUBLIC_WEBSITE_URL}/_next/image?url=%2Fpngs%2Fonyma%2Fesocial-event-list.png&w=1920&q=75`,
        width: 479,
        height: 479,
        alt: "Listagem dos eventos enviados para o eSocial com seu respective status",
      },
    ],
  },
};
