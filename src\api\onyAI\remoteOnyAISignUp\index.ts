import axios from "axios";
import { RemoteOnyAISignUp } from "./types";

export default async function remoteOnyAISignUp({
  name,
  lives,
  email,
  phone,
}: RemoteOnyAISignUp.Params): Promise<RemoteOnyAISignUp.Result> {
  try {
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_API_URL}/geraldo/auth/sign-up`,
      {
        name,
        lives,
        email,
        phone,
      },
      {
        headers: { "Content-Type": "application/json" },
      }
    );
    const { token, questionsLeft } = response.data;
    if (!token) {
      throw new Error("Não foi possível fazer o cadastro");
    }
    return { token, questionsLeft };
  } catch (error) {
    const message =
      error.response?.data?.error ||
      error.response?.data?.message ||
      error.message;
    throw new Error(message);
  }
}
