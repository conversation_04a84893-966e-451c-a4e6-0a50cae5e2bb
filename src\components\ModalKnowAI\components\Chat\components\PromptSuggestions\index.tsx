import React, { useState } from "react";
import { Slider } from "./components";
import { PromptSuggestionsProps } from "./types";
import styles from "./styles.module.scss";

const allSuggestions = [
  "O que faz a Onyma?",
  "O que é saúde ocupacional?",
  "O que é eSocial?",
  "O que a plataforma da Onyma oferece?",
];

export default function PromptSuggestions({
  onSuggestionClick,
}: PromptSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<string[]>(allSuggestions);

  const handleSuggestionClick = (suggestion: string) => {
    onSuggestionClick(suggestion);
  };

  return (
    <div className={styles.container}>
      <Slider items={suggestions} onItemSelect={handleSuggestionClick} />
    </div>
  );
}
