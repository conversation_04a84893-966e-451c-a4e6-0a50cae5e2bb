import styled, { css } from "styled-components";

interface ContainerProps {
  isMenuOpen: boolean;
}

export const Container = styled.div<ContainerProps>`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1;
  max-height: 0;
  overflow: hidden;

  background: ${({ theme }) => theme.colors.white};

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: ${({ theme }) => theme.spacings.xxxs};

  transition: all 1s ease-in-out;

  & > a {
    color: ${({ theme }) => theme.colors.gray_40};
  }

  @media (min-width: 980px) {
    display: none;
  }

  ${({ isMenuOpen }) =>
    isMenuOpen &&
    css`
      max-height: 300px;
    `}
`;

export const LinkQuotationButtonWrapper = styled.div`
  margin-bottom: 4rem;
`;
