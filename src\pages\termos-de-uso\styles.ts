import { Text } from "@onyma-ds/react";
import styled from "styled-components";

export const Container = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
`;

export const TermsHighlights = styled.section`
  background: linear-gradient(254.28deg, #27b5bf 0%, #17a9fb 100%);
  padding: 4rem 8rem;

  @media (max-width: 1200px) {
    padding: 4rem 6rem;
  }

  @media (max-width: 920px) {
    padding: 3rem 4rem;
  }

  @media (max-width: 720px) {
    padding: 2.5rem 2.5rem;
  }

  @media (max-width: 490px) {
    padding: 2rem 1rem;
  }

  & > h2 {
    color: ${({ theme }) => theme.colors.white};
    margin-bottom: 2rem;
  }
`;

export const TermsText = styled(Text).attrs({
  type: "body_02",
})`
  color: ${({ theme }) => theme.colors.white};

  &:not(:last-child) {
    margin-bottom: 1rem;
  }
`;

export const TermsMain = styled.section`
  background-color: ${({ theme }) => theme.colors.gray_98};
  padding: 4rem 8rem;

  @media (max-width: 1200px) {
    padding: 4rem 6rem;
  }

  @media (max-width: 920px) {
    padding: 3rem 4rem;
  }

  @media (max-width: 720px) {
    padding: 2.5rem 2.5rem;
  }

  @media (max-width: 490px) {
    padding: 2rem 1rem;
  }
`;

export const TermsMainSection = styled.section`
  &:not(:last-child) {
    margin-bottom: 4rem;
  }

  & > h3 {
    color: ${({ theme }) => theme.colors.black};
    margin-bottom: 1rem;
  }
`;

export const TermsTextMain = styled(TermsText).attrs({
  type: "body_02",
})`
  color: ${({ theme }) => theme.colors.gray_60};

  &:not(:last-child) {
    margin-bottom: 1rem;
  }
`;

export const TermsTextMainLi = styled(TermsTextMain).attrs({
  as: "li",
})`
  font-size: ${({ theme }) => theme.typographies.body_02.fontSize};
  font-weight: ${({ theme }) => theme.typographies.body_02.fontWeight};
  line-height: ${({ theme }) => theme.typographies.body_02.lineHeight};
  list-style: none;
  margin-bottom: 1rem;

  &:not(:last-child) {
    margin-bottom: 0;
  }

  &::before {
    content: "• ";
    font-size: 1.125rem;
  }
`;
