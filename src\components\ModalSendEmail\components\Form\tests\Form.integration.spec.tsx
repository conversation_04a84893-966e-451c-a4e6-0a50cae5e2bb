import { ToastContext } from "@onyma-ds/react";
import {
  cleanup,
  screen,
  waitFor,
  waitForElementToBeRemoved,
} from "@testing-library/react";
import { renderTheme } from "@/utils/renderTheme";
import { ApiContext } from "@/contexts/ApiContext";
import FormContextProvider from "../../../contexts/FormContext";
import Form from "..";
import * as Helpers from "./utils";

type SutTypes = {
  onHideFormMock: jest.Mock;
  remoteSendEmailToCommercialTeamMock: jest.Mock;
};

const makeSut = (): SutTypes => {
  const onHideFormMock = jest.fn();
  const remoteSendEmailToCommercialTeamMock = jest.fn(async () => {});
  renderTheme(
    <ToastContext.Provider value={{ addToast: jest.fn() }}>
      <ApiContext.Provider
        value={{
          remoteSendEmailToCommercialTeam: remoteSendEmailToCommercialTeamMock,
        }}
      >
        <FormContextProvider>
          <Form onHideForm={onHideFormMock} />
        </FormContextProvider>
      </ApiContext.Provider>
    </ToastContext.Provider>
  );
  return {
    onHideFormMock,
    remoteSendEmailToCommercialTeamMock,
  };
};

describe("<Form /> Integration", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cleanup();
  });

  describe("Component Dynamics", () => {
    it("should call onHideForm when submit a valid form", async () => {
      const { onHideFormMock } = makeSut();
      Helpers.prefillForm();
      Helpers.submitForm();
      await waitFor(() => {
        expect(onHideFormMock).toHaveBeenCalled();
      });
    });

    it("should call remoteSendEmailToCommercialTeam when submit a valid form", async () => {
      const { remoteSendEmailToCommercialTeamMock } = makeSut();
      Helpers.prefillForm();
      Helpers.submitForm();
      await waitFor(() => {
        expect(remoteSendEmailToCommercialTeamMock).toHaveBeenCalled();
      });
    });

    it("should show Spinner on submit a valid form", async () => {
      makeSut();
      Helpers.prefillForm();
      Helpers.submitForm();
      await waitFor(() => {
        const spinnerElement = screen.queryByTestId("spinner-modal-send-email");
        expect(spinnerElement).toBeTruthy();
      });
    });

    it("should disabled submit button when already submitting", async () => {
      makeSut();
      Helpers.prefillForm();
      Helpers.submitForm();
      const button = screen.getByTitle("Enviar mensagem para nossa equipe");
      await waitFor(() => {
        expect(button).toBeDisabled();
      });
    });
  });
});
