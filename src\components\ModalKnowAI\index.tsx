import { useState } from "react";

import { ModalAIBase } from "..";
import { Chat, Form, MeetOnyAI } from "./components";
import FormContextProvider from "./contexts/FormContext";

import { ModalSteps, Props } from "./types";
import { UserContextProvider } from "./contexts";

export default function ModalKnowAI({ show, onClose }: Props) {
  const [modalStep, setModalStep] = useState<ModalSteps>("meet-onyai");

  if (!show) return <></>;
  return (
    <ModalAIBase show={show} onClose={onClose}>
      <UserContextProvider>
        {modalStep === "meet-onyai" && (
          <MeetOnyAI onContinue={() => setModalStep("company-info-form")} />
        )}
        {modalStep === "company-info-form" && (
          <FormContextProvider>
            <Form onContinue={() => setModalStep("chat")} />
          </FormContextProvider>
        )}
        {modalStep === "chat" && <Chat />}
      </UserContextProvider>
    </ModalAIBase>
  );
}
