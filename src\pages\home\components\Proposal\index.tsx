import { SyntheticEvent, useEffect, useRef, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { Heading, Text } from "@onyma-ds/react";

import { pngPaths } from "@/assets/paths/pngPaths";
import * as SC from "./styles";

export default function Proposal() {
  const dashboardRHImageRef = useRef<HTMLImageElement>(null);

  const [spacingToIncrease, setSpacingToIncrease] = useState(0);

  const handleWindowResize = () => {
    const dashboardRHImageBounding =
      dashboardRHImageRef.current?.getBoundingClientRect();
    if (!dashboardRHImageBounding) return;
    const spacing = dashboardRHImageBounding.height / 2;
    setSpacingToIncrease(spacing);
  };

  const onLoadImage = (event: SyntheticEvent<HTMLImageElement, Event>) => {
    const dashboardRHImageBounding =
      event.currentTarget.getBoundingClientRect();
    const spacing = dashboardRHImageBounding.height / 2;
    setSpacingToIncrease(spacing);
  };

  useEffect(() => {
    window.addEventListener("resize", handleWindowResize);
    return () => window.removeEventListener("resize", handleWindowResize);
  }, []);

  return (
    <SC.Container id="introduction" extraPadding={spacingToIncrease}>
      <SC.Content>
        <Heading>
          Com a Onyma a sua empresa tem todas as soluções para emissão de ASOs,
          eSocial e muito mais!
        </Heading>
        <Text>
          Não perca mais tempo controlando e agendando os exames de seus
          colaboradores. A Onyma faz isso por você de forma fácil e
          descomplicada!
        </Text>
        <SC.ActionButtons>
          {/* <Link
            id="introduction-link-landing-page"
            href={process.env.NEXT_PUBLIC_LANDING_PAGE_URL || ""}
            target="_blank"
          >
            <SC.ButtonSimulation
              type="button"
              variant="white"
              title="Clique aqui para simular valor de acordo com a sua empresa"
            >
              Vamos simular o seu plano?
            </SC.ButtonSimulation>
          </Link> */}
          <Link id="go-to-contacts-section" href="/#contacts">
            <SC.ButtonToContact
              type="button"
              variant="white"
              buttonType="secondary"
              title="Clique aqui para falar com nossa equipe"
            >
              Fale com a equipe de vendas
            </SC.ButtonToContact>
          </Link>
        </SC.ActionButtons>
      </SC.Content>
      <SC.DashboardRHImage>
        <Image
          ref={dashboardRHImageRef}
          src={pngPaths.onyma.dashboardRHImage.path}
          fill
          quality={75}
          priority
          alt="Dashboard RH da Onyma Digital"
          onLoad={onLoadImage}
        />
      </SC.DashboardRHImage>
    </SC.Container>
  );
}
