import { faker } from "@faker-js/faker";
import { fireEvent, screen } from "@testing-library/dom";
import { act } from "@testing-library/react";
import { FormState } from "../../../contexts/hooks";

export const testValueByPlaceholder = (
  placeholder: string,
  value: string = ""
) => {
  const inputName = screen.getByPlaceholderText(placeholder);
  expect(inputName).toHaveDisplayValue(value);
};

export const testCheckedByLabel = (label: string, value: boolean = false) => {
  const inputName = screen.getByLabelText(label);
  expect(inputName).toHaveProperty("checked", value);
};

export const testTextIsInTheScreen = (text: string) => {
  const element = screen.queryByText(text);
  expect(element).toBeTruthy();
};

export const typeInputByPlaceholder = async (
  placeholder: string,
  value: string
) => {
  const input = screen.getByPlaceholderText(placeholder);
  fireEvent.change(input, { target: { value: value } });
};

export const checkOptionByLabel = (label: string) => {
  const option = screen.getByLabelText(label);
  fireEvent.click(option);
};

export const prefillForm = () => {
  typeInputByPlaceholder("Digite seu nome", faker.name.fullName());
  typeInputByPlaceholder("Digite seu cargo", "any_role");
  typeInputByPlaceholder(
    "Digite seu e-mail corporativo",
    faker.internet.email()
  );
  typeInputByPlaceholder("(00) 00000-0000", faker.phone.number("###########"));
  typeInputByPlaceholder("00.000.000/0000-00", "77.656.649/0001-90");
  typeInputByPlaceholder(
    "Informe o número de colaboradores",
    faker.datatype.number({ min: 1, max: 100 }).toString()
  );
  typeInputByPlaceholder(
    "Informe a média de admissões mensais",
    faker.datatype.number(100).toString()
  );
  typeInputByPlaceholder(
    "Informe a média de demissões mensais",
    faker.datatype.number(100).toString()
  );
  typeInputByPlaceholder(
    "Informe a média de demissões mensais",
    faker.datatype.number(100).toString()
  );
  checkOptionByLabel("Opção: modelo de trabalho presencial");
  checkOptionByLabel("Opção: interesse no serviço de saúde ocupacional");
  typeInputByPlaceholder("Digite aqui sua mensagem", faker.hacker.phrase());
};

export const submitForm = async () => {
  const form = screen.getByTestId("form");
  await fireEvent.submit(form);
};

export const initialFormState: FormState = {
  name: "",
  role: "",
  email: "",
  phone: "",
  cnpj: "",
  numberOfCollaborators: 1,
  averageMonthlyAdmissions: 1,
  averageMonthlyLayoffs: 1,
  workModel: "in-person",
  servicesOfInterest: [],
  message: "",
};
