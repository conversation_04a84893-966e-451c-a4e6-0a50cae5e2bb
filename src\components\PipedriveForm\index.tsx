import { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>ontaine<PERSON>,
  <PERSON><PERSON><PERSON>pinner,
  LoaderText,
} from "../ModalSendEmail/styles";

const FORM_URL =
  "https://webforms.pipedrive.com/f/oI2f4SWPlCGhrXvd8n0poveSQBUKoc0dytn64gcgH0FX5fHb61u7GG8flqiVc9Z";
const LOADER_SRC = "https://webforms.pipedrive.com/f/loader";

export function PipedriveWebForm() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (typeof window === "undefined") return;

    const existingScript = document.querySelector(
      `script[src="${LOADER_SRC}"]`
    );

    if (!existingScript) {
      const s = document.createElement("script");
      s.src = LOADER_SRC;
      s.async = true;

      s.onload = () => {
        // Aguarda um pouco mais para garantir que o formulário seja renderizado
        setTimeout(() => {
          setIsLoading(false);
        }, 300);
      };

      s.onerror = () => {
        setIsLoading(false);
      };

      document.body.appendChild(s);
    } else {
      // Script já existe, verifica se já carregou
      setTimeout(() => {
        setIsLoading(false);
      }, 200);
    }
  }, []);

  if (isLoading) {
    return (
      <LoaderContainer>
        <LoaderSpinner />
        <LoaderText>Carregando formulário...</LoaderText>
      </LoaderContainer>
    );
  }

  return <div className="pipedriveWebForms" data-pd-webforms={FORM_URL} />;
}
