import { useEffect, useMemo, useRef, useState } from "react";
import { Symbol } from "@onyma-ds/react";
import { Props } from "./types";
import styles from "./styles.module.scss";

export default function Slider({ items, onItemSelect }: Props) {
  const listRef = useRef<HTMLUListElement>(null);

  const [scrollRepeated, setScrollRepeated] = useState(0);
  const [isHover, setIsHover] = useState(false);

  const handleItemSelect = (item: string) => onItemSelect(item);
  const handleMouseEnter = () => setIsHover(true);
  const handleMouseLeave = () => setIsHover(false);

  const handleScroll = (event: React.UIEvent<HTMLUListElement, UIEvent>) => {
    const { scrollWidth, scrollLeft, clientWidth } = event.currentTarget;
    const scrollPercentage = scrollLeft / (scrollWidth - clientWidth);
    if (scrollRepeated >= 10) return setScrollRepeated(1);
    if (scrollPercentage > 0.9) {
      setScrollRepeated((prev) => prev + 1);
    }
  };

  const scrollToRight = (scrollQuantity: number = 5) => {
    if (listRef.current) {
      listRef.current.scrollLeft += scrollQuantity;
    }
  };

  const scrollToLeft = (scrollQuantity: number = 5) => {
    if (listRef.current) {
      listRef.current.scrollLeft -= scrollQuantity;
    }
  };

  useEffect(() => {
    const interval = setInterval(() => {
      if (listRef.current && !isHover) {
        scrollToRight();
      }
    }, 60);
    return () => {
      clearInterval(interval);
    };
  }, [listRef, items, isHover]);

  const itemsRender = useMemo(() => {
    const itemsToRender: string[] = items;
    for (let i = 0; i < scrollRepeated; i++) {
      itemsToRender.push(...items);
    }
    return itemsToRender;
  }, [items, scrollRepeated]);

  return (
    <div
      className={styles.container}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <button
        type="button"
        className={[
          styles.scrollButton,
          styles.left,
          isHover ? styles.hover : "",
        ].join(" ")}
        onClick={() => scrollToLeft(50)}
      >
        <Symbol name="chevron_left" size={28} />
      </button>
      <ul
        ref={listRef}
        className={styles.scrollContent}
        onScroll={handleScroll}
      >
        {itemsRender.map((suggestion, index) => (
          <li key={`${index}${suggestion}`} className={styles.suggestion}>
            <button
              title={suggestion}
              onClick={() => handleItemSelect(suggestion)}
            >
              {suggestion}
            </button>
          </li>
        ))}
      </ul>
      <button
        type="button"
        className={[
          styles.scrollButton,
          styles.right,
          isHover ? styles.hover : "",
        ].join(" ")}
        onClick={() => scrollToRight(50)}
      >
        <Symbol name="chevron_right" size={28} />
      </button>
    </div>
  );
}
