import * as yup from "yup";
import { formSchema } from "./Schema";
import { FormValidation } from "./types";

export async function validateForm(form: any) {
  let errors: FormValidation = {};
  try {
    await formSchema.validate(form, {
      abortEarly: false,
    });
    return null;
  } catch (error) {
    (error as yup.ValidationError).inner.forEach((e) => {
      if (e.path) {
        errors = {
          ...errors,
          [e.path]: { error: e.message },
        };
      }
    });
    return errors;
  }
}
