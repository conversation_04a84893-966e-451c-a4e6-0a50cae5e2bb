import Link from "next/link";
import { useEffect, useState } from "react";
import { Switch } from "@onyma-ds/react";
import * as SC from "./styles";
import { CookieTypes } from "./types";

export default function CookiesAlert() {
  const [show, setShow] = useState(true);
  const [cookiesAccept] = useState<CookieTypes[]>(["essentials"]);
  const [showCookiesManager, setShowCookiesManager] = useState(false);

  const handleCookiesManager = () => {
    if (!showCookiesManager) return setShowCookiesManager(true);
    setShow(false);
  };

  const handleAcceptAllCookies = () => {
    setShow(false);
  };

  useEffect(() => {
    sessionStorage.setItem(
      "@onyma-digital/cookies",
      JSON.stringify(cookiesAccept)
    );
  }, [cookiesAccept]);

  if (!show) return <></>;

  return (
    <SC.Container>
      <SC.Title type="heading_04">Este website utiliza cookies</SC.Title>
      <SC.Excerpt type="body_02">
        Utilizamos cookies para analisar o nosso tráfego, melhorar a sua
        experiência e personalizar conteúdos. Para saber mais, leia nossa{" "}
        <Link href="/documents/politica-de-privacidade.pdf" target="_blank">
          Política de Privacidade.
        </Link>
      </SC.Excerpt>
      {showCookiesManager && (
        <SC.CookiesManager>
          <Switch.Container>
            <Switch
              disabled
              defaultChecked={cookiesAccept.includes("essentials")}
            />
            <Switch.Label disabled>Essenciais (sempre ativo)</Switch.Label>
          </Switch.Container>
        </SC.CookiesManager>
      )}
      <SC.ActionButtons>
        <SC.MainButton
          variant="secondary"
          buttonType="primary"
          style={{ order: showCookiesManager ? 2 : 1 }}
          onClick={handleAcceptAllCookies}
        >
          {/* Permitir todos os cookies */}
          Entendi e fechar
        </SC.MainButton>
        {/* <SC.SecondaryButton
          variant="secondary"
          buttonType="secondary"
          style={{ order: showCookiesManager ? 1 : 2 }}
          onClick={handleCookiesManager}
        >
          {showCookiesManager
            ? "Permitir itens selecionados"
            : "Configurações de cookies"}
        </SC.SecondaryButton> */}
      </SC.ActionButtons>
    </SC.Container>
  );
}
