#companyInfo {
  padding: 4rem;
  width: min(90vw, 720px);
  max-height: 100vh;
  overflow-y: auto;

  @media (max-width: 720px) {
    padding: 2rem;
  }
}

.modalHeader {
  width: 100%;
  text-align: center;

  & > h3 {
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 120%;

    margin-bottom: 0.5rem;
  }

  & > h4 {
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 140%;

    margin-bottom: 4rem;
  }

  @media (max-width: 720px) {
    & > h3 {
      font-size: 1rem;
    }

    & > h4 {
      font-size: 0.75rem;

      margin-bottom: 3rem;
    }
  }
}

.modalBody {
  width: 100%;
}

.modalFooter {
  width: 100%;
  margin-top: 2.5rem;

  & > button {
    width: 100%;
    background-color: #7159eb;
    border: 1px solid #7159eb;
    border-radius: 4px;
    font-family: "Roboto", sans-serif;
    font-size: 1rem;
    font-weight: 600;
    line-height: 140%;
    color: #fff;
    padding: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;

    &:disabled {
      background-color: #cccccc;
      border-color: #cccccc;
      color: #999999;
    }

    @media (max-width: 720px) {
      font-size: 0.875rem;
      padding: 0.75rem;

      & > span {
        font-size: 1.25rem;
      }
    }
  }
}

.gridFields {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;

  @media (max-width: 570px) {
    grid-template-columns: 1fr;
  }
}

.centeredField {
  display: grid;
  place-items: center;

  & > div {
    width: 284px;

    @media (max-width: 720px) {
      width: 100%;
    }
  }
}

.termsOfUse {
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 140%;
  text-align: center;
  color: #fff;

  & > a {
    color: #ffcc00;
  }
}

.onyAIWarning {
  font-size: 0.625rem;
  font-weight: 400;
  line-height: 0.875rem;
  text-align: center;
  margin-bottom: 1rem;
}

.loading {
  font-size: 7px;
}

@keyframes dash {
  to {
    stroke-dashoffset: 136;
  }
}

.triangle {
  stroke-dasharray: 17;
  animation: dash 1.5s cubic-bezier(0.35, 0.04, 0.63, 0.95) infinite;
}
