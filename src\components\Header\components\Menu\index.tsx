import { Links, LinkQuotationButton } from "..";
import * as SC from "./styles";

interface MenuProps {
  isMenuOpen: boolean;
  onClose: () => void;
}

export const Menu = ({ isMenuOpen, onClose }: MenuProps) => {
  return (
    <SC.Container data-testid="header-menu-container" isMenuOpen={isMenuOpen}>
      <Links onClose={onClose} />
      <SC.LinkQuotationButtonWrapper>
        <LinkQuotationButton onClose={onClose} />
      </SC.LinkQuotationButtonWrapper>
    </SC.Container>
  );
};
