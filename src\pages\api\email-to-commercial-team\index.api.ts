import { NextApiRequest, NextApiResponse } from "next";
import { sendEmailToCommercialTeamUseCase } from "./useCases/sendEmailToCommercialTeamUseCase";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const { form } = req.body;

    if (req.method !== "POST") {
      return res.status(404).send(null);
    }

    const recipients = process.env.SEND_EMAIL_RECIPIENT?.split(",") || [];
    await sendEmailToCommercialTeamUseCase({
      recipient: recipients,
      variables: form,
    });
    return res.status(200).json({ message: "Formulário enviado com sucesso!" });
  } catch (error) {
    res.status(500).json({ error: "Erro desconhecido" });
  }
}
