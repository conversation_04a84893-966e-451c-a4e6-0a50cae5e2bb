import styled from "styled-components";

export const Container = styled.div`
  background: linear-gradient(254.28deg, #27b5bf 0%, #17a9fb 100%);
  width: 100%;
  padding: 4rem 8rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  gap: 4rem;
  position: relative;

  @media (max-width: 1200px) {
    padding: 4rem 6rem;
  }

  @media (max-width: 920px) {
    padding: 3rem 4rem;
  }

  @media (max-width: 720px) {
    padding: 2.5rem 2.5rem;
  }

  @media (max-width: 490px) {
    padding: 2rem 1rem;
  }
`;

export const HeartImageBoxCrop = styled.div`
  width: 40%;
  height: 40%;
  overflow: hidden;
  position: absolute;
  right: 0;
  bottom: 0;

  @media (max-width: 1200px) {
    width: 50%;
  }

  @media (max-width: 920px) {
    width: 60%;
  }

  @media (max-width: 720px) {
    width: 70%;
    height: 20%;
  }

  @media (max-width: 490px) {
    width: 85%;
    height: 22%;
  }

  @media (max-width: 440px) {
    width: 90%;
    height: 16%;
  }
`;

export const HeartImageBox = styled.div`
  position: relative;
  object-fit: cover;
  width: 120%;
  height: 140%;
  aspect-ratio: 1.67;
  opacity: 0.4;
  color: ${({ theme }) => theme.colors.white};
  mix-blend-mode: soft-light;
`;

export const Content = styled.div`
  position: relative;
  z-index: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8rem;

  @media (max-width: 1200px) {
    gap: 4rem;
  }

  @media (max-width: 920px) {
    gap: 2rem;
  }

  @media (max-width: 720px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
`;

export const Apresentation = styled.div`
  width: 100%;

  & > h2 {
    color: ${({ theme }) => theme.colors.white};
    margin-bottom: 2rem;
  }

  & > article > p {
    color: ${({ theme }) => theme.colors.white};

    &:not(:last-child) {
      margin-bottom: 1rem;
    }
  }
`;

export const OnymaImageBox = styled.div`
  position: relative;
  width: 100%;
  max-height: 342px;
  aspect-ratio: 1.4;
`;

export const MediaBox = styled.section`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  position: relative;
  z-index: 1;

  & > h3 {
    color: ${({ theme }) => theme.colors.white};
  }

  * {
    ::-webkit-scrollbar {
      display: none;
    }

    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
`;

export const MediaLogos = styled.div`
  padding-top: 1rem;
  display: flex;
  align-items: center;
  gap: 2rem;
  overflow-x: auto;
`;
