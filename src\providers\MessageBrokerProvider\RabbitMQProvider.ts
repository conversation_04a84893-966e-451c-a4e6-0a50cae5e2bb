import amqp from "amqplib";
import BrokerInterface from "./Broker.interface";

class RabbitMQ<PERSON>rovider implements BrokerInterface {
  private connection: amqp.Connection | null = null;
  private processing = false;
  private callbackQueue: any[] = [];
  private static INSTANCE: Rabbit<PERSON>Q<PERSON><PERSON>ider;

  constructor() {}

  static getInstance() {
    if (!this.INSTANCE) {
      RabbitMQProvider.INSTANCE = new this();
    }

    return RabbitMQProvider.INSTANCE;
  }

  async connect(): Promise<void> {
    try {
      this.connection = await amqp.connect(process.env.MESSAGE_QUEUE_URL || "");
    } catch (error) {
      console.log("Error connecting to RabbitMQ", error);
    }
  }

  async close(): Promise<void> {
    if (this.connection) {
      await this.connection.close();
    }
  }

  async on(queueName: string, callback: Function): Promise<void> {
    const channel = await this.connection!.createChannel();
    await channel.assertQueue(queueName);
    channel.consume(queueName, async (msg) => {
      this.callbackQueue.push(() => {
        return callback(JSON.parse(msg!.content.toString())).then(() =>
          channel.ack(msg!)
        );
      });
      if (!this.processing) {
        this.processing = true;
        await this.processCallbackQueue();
      }
    });
  }

  async publish(
    exchangeName: string,
    bindingKey = "",
    data: any
  ): Promise<void> {
    const channel = await this.connection!.createChannel();
    channel.publish(
      exchangeName,
      bindingKey,
      Buffer.from(JSON.stringify(data))
    );
    await channel.close();
  }

  private async processCallbackQueue(): Promise<void> {
    if (this.callbackQueue.length === 0) {
      this.processing = true;
      return;
    }
    const nextCallback = this.callbackQueue.shift();
    await nextCallback();
    await this.processCallbackQueue();
  }
}

export default RabbitMQProvider;
