import Link from "next/link";
import * as SC from "./styles";

export const Links = ({ onClose }: { onClose: () => void }) => {
  return (
    <>
      <Link
        href="/quem-somos"
        title="Navegar para a página 'Quem Somos?'"
        onClick={onClose}
      >
        <SC.StyledText>Quem somos?</SC.StyledText>
      </Link>
      <Link
        href="/#nossos-clientes"
        title="Navegar para a página 'Clientes'"
        onClick={onClose}
      >
        <SC.StyledText>Clientes</SC.StyledText>
      </Link>
      <Link
        href="/#o-que-fazemos"
        title="Navegar para a página 'O que fazemos'"
        onClick={onClose}
      >
        <SC.StyledText>O que fazemos</SC.StyledText>
      </Link>
      <Link
        href="/#depoimentos"
        title="Navegar para a página 'Depoimentos'"
        onClick={onClose}
      >
        <SC.StyledText>Depoimentos</SC.StyledText>
      </Link>
      <Link
        href="/#perguntas-frequentes"
        title="Navegar para a página 'Perguntas frequentes'"
        onClick={onClose}
      >
        <SC.StyledText>Perguntas frequentes</SC.StyledText>
      </Link>
    </>
  );
};
