import { Modal } from "@onyma-ds/react";
import { SubmitButton } from "..";

import * as SCModal from "../../styles";
import * as SC from "./styles";
import { EmailSentProps } from "./types";

export default function EmailSent({ onClose }: EmailSentProps) {
  return (
    <>
      <Modal.Header>
        <Modal.Title>Mensagem enviada</Modal.Title>
        <Modal.Subtitle>
          Recebemos sua mensagem. Em breve alguém de nossa equipe entrará em
          contato com você!
        </Modal.Subtitle>
      </Modal.Header>
      <SCModal.ModalBody>
        <SC.SymbolCheck aria-label="Sua mensagem foi enviada com sucesso" />
      </SCModal.ModalBody>
      <Modal.Footer>
        <SubmitButton
          type="button"
          title="Vou aguardar contato"
          onClick={onClose}
        >
          Ok, entendi
        </SubmitButton>
      </Modal.Footer>
    </>
  );
}
