import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { PageLayout } from "@/layouts";
import * as SC from "./styles";

export default function Page404() {
  const router = useRouter();
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    if (countdown > 0) {
      const timeout = setTimeout(() => {
        setCountdown((prevCountdown) => prevCountdown - 1);
      }, 1000);

      return () => clearTimeout(timeout);
    } else {
      router.push("/");
    }
  }, [countdown, router]);

  return (
    <PageLayout nextSeoProps={{ noindex: true }}>
      <SC.Container>
        <h1>Página não encontrada</h1>
        <p>
          Você será redirecionado para a página principal em{" "}
          <strong>{countdown} segundos.</strong>
        </p>
      </SC.Container>
    </PageLayout>
  );
}
