$breakpoint: 540px;

.container {
  ::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */

  position: absolute;
  inset: 0;
  white-space: nowrap;
}

.scrollContent {
  list-style: none;
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow-x: scroll;
}

.suggestion {
  width: fit-content;
  height: fit-content;
  cursor: grabbing;

  &:not(:last-child) {
    margin-right: 0.75rem;
  }

  & > button {
    white-space: nowrap;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 120%;
    color: #7159eb;
    background-color: rgba(113, 89, 235, 0.2);
    border: 1px solid #7159eb;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;

    @media (max-width: $breakpoint) or (max-height: $breakpoint) {
      padding: 0.125rem 0.5rem;
      line-height: 120%;
    }
  }
}

.scrollButton {
  background-color: transparent;
  border-color: transparent;
  color: #a596f2;
  padding: 0;
  cursor: pointer;

  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  opacity: 0;
  visibility: hidden;

  @media (max-width: $breakpoint) {
    opacity: 1;
    visibility: visible;
  }

  &.left {
    left: 0px;
  }

  &.right {
    right: 0px;
  }

  &.hover {
    opacity: 1;
    visibility: visible;
  }
}
