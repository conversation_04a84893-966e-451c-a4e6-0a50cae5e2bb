$breakpoint: 540px;

.container {
  width: min(90vw, 1312px);
  min-height: 100vh;
  min-height: -webkit-fill-available;
  max-height: 100vh;
  height: min(90vh, 672px);
  overflow-y: auto;
  overflow-x: hidden;
  padding: 3rem 2rem 2rem;
  display: grid;

  @media (max-width: $breakpoint) {
    width: 100vw;
    height: 100svh;
    padding: 1.5rem;
  }

  @media (max-height: $breakpoint) {
    width: 100vw;
    height: 100svh;
  }
}

.chatHeader {
  height: min-content;
  margin-bottom: 2rem;
  transform: translateX(100%);
  animation: chatImageFromCenterToLeft 3s ease-in-out forwards;
  animation-delay: 3s;

  @media (max-width: $breakpoint) {
    transform: translateX(0);
    margin-bottom: 0;
    animation: none;
  }

  @media (max-height: $breakpoint) {
    margin-bottom: 1rem;
  }

  & > div {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;

    & > h3 {
      font-size: 1.25rem;
      font-weight: 600;
      line-height: 140%;
      color: #d9d2fa;
      text-align: center;
      vertical-align: middle;
    }
  }
}

.chatMain {
  display: grid;
  grid-template-columns: 1fr 2fr;
  grid-template-rows: auto 1fr;
  gap: 2rem;

  @media (max-width: $breakpoint) {
    overflow: hidden;
    grid-template-columns: 1fr;
    grid-template-rows: auto auto 1fr;
    gap: 0;
  }
}

@keyframes chatImageFromCenterToLeft {
  to {
    transform: translateX(0);
  }
}

@keyframes chatImageFromCenterToTop {
  to {
    transform: translateY(0);
    padding: 1.75rem;
  }
}

.chatImage {
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  transform: translateX(100%);
  animation: chatImageFromCenterToLeft 3s ease-in-out forwards;
  animation-delay: 3s;
  position: relative;

  @media (max-width: $breakpoint) {
    transform: translateY(150%);
    animation: chatImageFromCenterToTop 3s ease-in-out forwards;
    animation-delay: 3s;
  }
}

@keyframes chatInterfaceFromRight {
  from {
    visibility: hidden;
    opacity: 0;
    transform: translateX(150%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes chatInterfaceFromBottom {
  from {
    opacity: 0;
    visibility: hidden;
    transform: translateY(150%);
  }
  to {
    transform: translateX(0);
  }
}

.chatInterface {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 1.5rem;
  overflow: hidden;
  visibility: visible;
  opacity: 1;
  transform: translateX(150%);
  animation: chatInterfaceFromRight 3s ease-in-out forwards;
  animation-delay: 3s;

  grid-row: span 2;

  @media (max-width: $breakpoint) {
    opacity: 1;
    transform: translatey(100%);
    animation: chatInterfaceFromBottom 3s ease-in-out forwards;
    animation-delay: 3s;
    grid-row: 3;
  }

  @media (max-height: $breakpoint) {
    gap: 0.75rem;
  }
}

.messagesAndCounterWrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 0.5rem;

  @media (max-width: $breakpoint) or (max-height: $breakpoint) {
    gap: 0.25rem;
  }
}

.callButtons {
  margin-top: 1px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.chatInputBox {
  width: 100%;
  display: flex;
  gap: 0.75rem;

  & > input {
    width: 100%;

    background: #ffffff;
    border: 1px solid #7159eb;
    border-radius: 4px;
    padding: 0.75rem;

    color: #333;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 140%;

    &::placeholder {
      color: #ccc;
    }
  }

  & > button {
    color: #fff;
    background: #7159eb;
    border-radius: 4px;
    border: 0;
    padding: 0.75rem;

    display: grid;
    place-items: center;
  }

  @media (max-width: $breakpoint) or (max-height: $breakpoint) {
    gap: 0.25rem;

    & > input {
      padding: 0.5rem;
    }

    & > button {
      padding: 0.5rem;
    }
  }
}

@keyframes floatAnimation {
  0% {
    transform: translate(0px, 0px);
  }
  20% {
    transform: translate(-4px, -4px);
  }
  40% {
    transform: translate(4px, 4px);
  }
  60% {
    transform: translate(4px, -4px);
  }
  80% {
    transform: translate(-4px, 4px);
  }
  100% {
    transform: translate(0px, 0px);
  }
}

@keyframes shrinkLogo {
  0% {
    max-width: 192px;
  }
  50% {
    max-width: 192px;
  }
}

.logoBox {
  width: 100%;
  max-width: 192px;
  animation: floatAnimation 5s 3s ease-in-out infinite;
  position: relative;

  @media (max-width: $breakpoint) {
    animation: floatAnimation 5s 3s ease-in-out infinite,
      shrinkLogo 6s ease-in-out forwards;
    max-width: 128px;
  }
}

@keyframes hideBalon {
  from {
    display: block;
    visibility: visible;
    opacity: 1;
  }
  to {
    display: none;
    visibility: hidden;
    opacity: 0;
  }
}

.chatBaloon {
  background-color: #fff;
  color: #023d68;

  font-size: 0.875rem;
  font-weight: 400;
  line-height: 140%;
  text-align: center;

  width: max-content;
  padding: 0.75rem 1.5rem;
  border-radius: 1.5rem 1.5rem 1.5rem 0;

  position: absolute;
  bottom: 100%;
  left: 100%;

  animation: hideBalon 0.1s forwards;
  animation-delay: 3s;

  @media (max-width: $breakpoint) {
    border-bottom-left-radius: 1.5rem;
    left: 50%;
    transform: translate(-50%, -2.5rem);
    padding: 0.5rem 1rem;

    &::after {
      content: "";
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translate(-50%, -1px);
      width: 0;
      height: 0;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-top: 8px solid #fff;
    }
  }
}

@keyframes rotateOnce {
  to {
    transform: rotate(360deg);
  }
}

.logoBoxGlow {
  &::before {
    content: "";
    position: absolute;
    inset: 0%;
    background-color: #d9d2fa;
    filter: blur(40px);
    border-radius: 50%;
    z-index: -1;
    transition: opacity 1s ease-in-out, inset 1s ease-in-out;
  }

  @media (max-width: 1000px) {
    padding: 32px;

    &::before {
      inset: 0;
      filter: blur(32px);
    }
  }

  @media (max-width: 780px) {
    padding: 8px;

    &::before {
      filter: blur(16px);
    }
  }
}

@keyframes glowAnimation {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
    inset: -10%;
    filter: blur(32px);
  }
  100% {
    opacity: 0.5;
  }
}

.opNone {
  &::before {
    opacity: 0;
  }
}

.opFull {
  &::before {
    animation: glowAnimation 1s ease-in-out infinite;
  }
}

.opHalf {
  &::before {
    opacity: 0.5;
  }
}

.actionButton {
  width: min-content;
  height: 100%;
  white-space: nowrap;
  padding: 4px 8px;
  margin-bottom: 1px;
  border-radius: 4px;
  font-size: 0.875rem;
  display: flex;
  place-items: center;
  align-items: center;
  gap: 4px;

  @media (max-width: $breakpoint) or (max-height: $breakpoint) {
    font-size: 0.75rem;
    padding: 2px 8px;
  }

  span:first-child {
    font-size: 16px;
  }

  @media (max-width: 1280px) {
    span:last-child {
      display: none;
    }
  }
}

.buttonBeOnyma {
  background-color: transparent;
  color: #d9d2fa;
  border: 1px solid #d9d2fa;
  font-size: 0.75rem;
}

.emailButton {
  background-color: #7159eb;
  color: #fff;
  border: 1px solid #7159eb;
  font-size: 0.75rem;
}

.whatsappButton {
  background-color: #25d366;
  color: #fff;
  border: 1px solid #25d366;
  font-size: 0.75rem;
}

.videoCallButton {
  background-color: #17a9fb;
  color: #fff;
  border: 1px solid #17a9fb;
  font-size: 0.75rem;
}

.rotate {
  animation: rotateOnce 1s linear forwards;
}
