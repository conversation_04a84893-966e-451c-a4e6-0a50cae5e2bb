import { Heading } from "@onyma-ds/react";
import styled from "styled-components";

export const QuestionList = styled.div`
  display: flex;
  flex-direction: column;

  &:first-child {
    padding-right: 1rem;
  }

  &:last-child {
    padding-left: 1rem;
  }
`;

export const QuestionsBox = styled.div`
  display: grid;
  grid-template-columns: 50% 50%;
`;

export const Container = styled.div`
  background-color: #e0f3fe;
  padding: 6.25rem;

  @media (max-width: 1235px) {
    padding: 3.125rem;
  }

  @media (max-width: 1128px) {
    padding: 1.5rem;
  }

  @media (max-width: 992px) {
    ${QuestionsBox} {
      grid-template-columns: 100%;
    }

    ${QuestionList} {
      padding: 0;
    }
  }
`;

export const Title = styled(Heading).attrs({
  type: "heading_01",
})`
  color: ${({ theme }) => theme.colors.tertiary};
  text-align: center;
  margin-bottom: 4rem;
`;
