import { PropsWithChildren, createContext, useContext, useState } from "react";
import { UserContextData } from "./types";

export const UserContext = createContext<UserContextData>(
  {} as UserContextData
);

export default function UserContextProvider({ children }: PropsWithChildren) {
  const [token, setToken] = useState("");
  const [questionsLeft, setQuestionsLeft] = useState(5);

  return (
    <UserContext.Provider
      value={{
        token,
        setToken,
        questionsLeft,
        setQuestionsLeft,
      }}
    >
      {children}
    </UserContext.Provider>
  );
}

export const useUser = () => useContext(UserContext);
