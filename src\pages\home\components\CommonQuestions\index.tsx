import { useMemo } from "react";
import { Contacts, QuestionAccordion } from "./components";
import { commonQuestionsData } from "./data";
import * as SC from "./styles";

export default function CommonQuestions() {
  const organizeQuestions = useMemo(() => {
    const commonQuestionsOrganized: typeof commonQuestionsData[] = [[], []];
    commonQuestionsData.forEach((question, index) => {
      if (index % 2 === 0) {
        commonQuestionsOrganized[0].push(question);
      } else {
        commonQuestionsOrganized[1].push(question);
      }
    });
    return commonQuestionsOrganized;
  }, []);

  return (
    <SC.Container id="perguntas-frequentes">
      <SC.Title>Perguntas frequentes</SC.Title>
      <SC.QuestionsBox>
        {organizeQuestions.map((questions, index) => (
          <SC.QuestionList key={`question-column-${index}`}>
            {questions.map((question) => (
              <QuestionAccordion
                key={`question-${question.title}`}
                title={question.title}
                answer={question.answer}
              />
            ))}
          </SC.QuestionList>
        ))}
      </SC.QuestionsBox>
      <Contacts />
    </SC.Container>
  );
}
