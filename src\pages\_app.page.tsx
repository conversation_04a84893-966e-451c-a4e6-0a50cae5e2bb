import type { AppProps } from "next/app";
import { ThemeProvider } from "styled-components";
import { DefaultSeo } from "next-seo";
import { ToastProvider } from "@onyma-ds/react";

import { GlobalStyles, theme } from "@/styles";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Header } from "@/components";
import { GlobalLayout } from "@/layouts";
import { ApiContextProvider } from "@/contexts/ApiContext";
import { defaultSEOConfig } from "@/utils/seo/defaultSEOConfig";
import Head from "next/head";

export default function App({ Component, pageProps }: AppProps) {
  return (
    <>
      <Head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0"
        />
      </Head>
      <ThemeProvider theme={theme}>
        <ToastProvider>
          <ApiContextProvider>
            <GlobalStyles />
            <DefaultSeo {...defaultSEOConfig} />
            <CookiesAlert />
            <GlobalLayout>
              <Header />
              <Component {...pageProps} />
              <Footer />
            </GlobalLayout>
          </ApiContextProvider>
        </ToastProvider>
      </ThemeProvider>
    </>
  );
}
