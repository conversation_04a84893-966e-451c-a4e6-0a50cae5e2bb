import { FormEvent, createContext } from "react";
import { FormContextData, FormContextProviderProps } from "./types";
import { useFormState } from "../hooks";

export const FormContext = createContext<FormContextData>(
  {} as FormContextData
);

export default function FormContextProvider({
  children,
}: FormContextProviderProps) {
  const { formState, formDispatch, errors, validate } = useFormState();

  const onSubmit =
    (callback: Function) => async (event: FormEvent | undefined) => {
      if (event) event.preventDefault();
      const errorsData = await validate();
      if (!errorsData) return callback();
    };

  return (
    <FormContext.Provider value={{ formState, formDispatch, errors, onSubmit }}>
      {children}
    </FormContext.Provider>
  );
}
