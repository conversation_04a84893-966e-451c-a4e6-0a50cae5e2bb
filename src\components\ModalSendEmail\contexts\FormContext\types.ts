import { FormEvent, ReactNode } from "react";
import { FormState } from "../hooks";
import { FormDispatch } from "../hooks/useFormState/types";
import { FormValidation } from "../validation";

export type FormContextData = {
  formState: FormState;
  errors: FormValidation<FormState>;
  formDispatch: FormDispatch;
  onSubmit: (callback: Function) => (event: FormEvent | undefined) => void;
};

export type FormContextProviderProps = {
  children: ReactNode;
};
