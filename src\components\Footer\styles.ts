import { Symbol } from "@onyma-ds/react";
import styled from "styled-components";

export const Container = styled.footer`
  padding: 4rem 8rem;
  display: flex;
  flex-direction: column;

  @media (max-width: 1200px) {
    padding: 3rem 6rem;
  }

  @media (max-width: 920px) {
    padding: 3rem 4rem;
  }

  @media (max-width: 720px) {
    padding: 2.5rem 3rem;
  }

  @media (max-width: 490px) {
    padding: 2rem 1rem;
  }
`;

const Section = styled.section`
  padding: 2rem 0;

  &:not(:last-child) {
    border-bottom: 1px solid ${({ theme }) => theme.colors.gray_96};
  }
`;

export const OnymaLove = styled(Section)`
  display: grid;
  grid-template-columns: 1fr 20px;
  align-items: center;

  & > p > img {
    margin: 0 4px;
    position: relative;
    top: 2px;
  }
`;

export const Redirects = styled(Section)`
  display: flex;
  align-items: center;
  gap: 2rem;

  & > a {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  @media (max-width: 720px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
  }

  @media (max-width: 490px) {
    gap: 1rem;
  }
`;

export const LinkText = styled.p`
  color: ${({ theme }) => theme.colors.gray_60};
  font-size: 1rem;
`;

export const OutwardSymbol = styled(Symbol).attrs({
  name: "arrow_outward",
})`
  font-size: 22px;
  color: ${({ theme }) => theme.colors.gray_60};

  @media (max-width: 720px) {
    font-size: 20px;
  }
`;

export const Localization = styled(Section)`
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;

  @media (max-width: 660px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  & > p {
    color: ${({ theme }) => theme.colors.gray_60};
    line-height: normal;
  }

  h6 {
    color: ${({ theme }) => theme.colors.gray_60};
    margin-left: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
  }
`;
