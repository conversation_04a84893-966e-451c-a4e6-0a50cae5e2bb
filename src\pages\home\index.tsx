import { PageLayout } from "@/layouts";
import {
  BannerCall,
  CommonQuestions,
  Feedback,
  Functionalities,
  OurClients,
  Proposal,
} from "./components";
import { seoConfig } from "./seoConfig";
import * as SC from "./styles";

export default function Home() {
  return (
    <PageLayout nextSeoProps={seoConfig}>
      <SC.Container id="home-page">
        <Proposal />
        <SC.Content>
          <OurClients />
          <Functionalities />
          <SC.Feedbacks id="depoimentos">
            <Feedback author="meliuzJuliana" />
            <Feedback author="pipoSaudePriscilla" />
          </SC.Feedbacks>
          <CommonQuestions />
        </SC.Content>
        {/* <BannerCall /> */}
      </SC.Container>
    </PageLayout>
  );
}
