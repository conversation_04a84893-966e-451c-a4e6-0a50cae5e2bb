import * as masks from "@/utils/masks";
import type { Action } from "./types";

export function applyMasks(payload: Action["payload"]): Action["payload"] {
  switch (payload.field) {
    case "phone":
      return {
        ...payload,
        value: masks.phoneMask(String(payload.value)),
      };
    case "cnpj":
      return {
        ...payload,
        value: masks.cnpjMask(String(payload.value)),
      };
    default:
      return payload;
  }
}
