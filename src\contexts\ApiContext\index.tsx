import { createContext, useContext } from "react";
import remoteSendEmailToCommercialTeam from "@/api/remoteSendEmailToCommercialTeam";
import { remoteOnyAISignIn, remoteOnyAISignUp } from "@/api/onyAI";
import { ApiContextData, ApiContextProviderProps } from "./types";

export const ApiContext = createContext<ApiContextData>({} as ApiContextData);

export function ApiContextProvider({ children }: ApiContextProviderProps) {
  return (
    <ApiContext.Provider
      value={{
        remoteSendEmailToCommercialTeam,
        onyAI: {
          signUp: remoteOnyAISignUp,
          signIn: remoteOnyAISignIn,
        },
      }}
    >
      {children}
    </ApiContext.Provider>
  );
}

export const useApi = () => useContext(ApiContext);
