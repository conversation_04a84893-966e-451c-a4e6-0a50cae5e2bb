import { createGlobalStyle } from "styled-components";

export const GlobalStyles = createGlobalStyle`
    :root {
        color-scheme: only light;
    }

    * {
        box-sizing: border-box;
        padding: 0;
        margin: 0;
        scroll-behavior: smooth;
    }

    body {
        -webkit-font-smoothing: antialiased;
        background-color: ${({ theme }) => theme.colors.white};
        color: ${({ theme }) => theme.colors.gray_40};
        padding-bottom: env(safe-area-inset-bottom);
    }

    html {
        font-size: ${({ theme }) => theme.typographies.body_02.fontSize};
        font-weight: ${({ theme }) => theme.typographies.body_02.fontWeight};
        font-family: ${({ theme }) => theme.typographies.body_02.fontFamily};
        line-height: ${({ theme }) => theme.typographies.body_02.lineHeight};
    }

    a {
        text-decoration: none;
        cursor: pointer;
        
        &:hover {
            filter: brightness(0.7);
        }
    }

    button {
        cursor: pointer;
        
        &:hover:not(:disabled) {
            filter: brightness(0.7);
        }
    }

    @media (max-width: 1920px) {
        html {
            font-size: 112.5%;
        }
    }

    @media (max-width: 1440px) {
        html {
            font-size: 100%;
        }
    }

    @media (max-width: 1080px) {
        html {
            font-size: 93.75%;
        }
    }

    @media (max-width: 720px) {
        html {
            font-size: 87.5%;
        }
    }

    @media (max-width: 576px) {
        html {
            font-size: 75%;
        }
    }
`;
