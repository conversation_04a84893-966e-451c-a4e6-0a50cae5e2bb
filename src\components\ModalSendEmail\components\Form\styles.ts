import { Symbol, TextareaBox } from "@onyma-ds/react";
import styled from "styled-components";

export const MultipleChoice = styled.div`
  font-size: 0.875rem;
  align-self: flex-start;
`;

export const Textarea = styled(TextareaBox)`
  min-height: 104px;
`;

export const IconInfo = styled(Symbol).attrs({
  name: "info",
  size: 16,
})`
  color: ${({ theme }) => theme.colors.tertiary};
  margin: 0 0 1px 2px;
`;

export const SCModalBody = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;

  iframe {
    border: none;
    min-width: 420px;
    width: 100%;
    min-height: 90vh;
  }
`;
