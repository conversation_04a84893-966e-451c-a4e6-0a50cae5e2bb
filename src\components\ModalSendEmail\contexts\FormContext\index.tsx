import { createContext, FormEvent } from "react";
import { useFormState } from "../hooks";
import { FormContextData, FormContextProviderProps } from "./types";

export const FormContext = createContext<FormContextData>(
  {} as FormContextData
);

export default function FormContextProvider({
  children,
}: FormContextProviderProps) {
  const { formState, errors, formDispatch, validate } = useFormState();

  const onSubmit =
    (callback: Function) => async (event: FormEvent | undefined) => {
      if (event) event.preventDefault();
      const errorsData = await validate();
      if (!errorsData) return callback();
    };

  return (
    <FormContext.Provider value={{ formState, errors, formDispatch, onSubmit }}>
      {children}
    </FormContext.Provider>
  );
}
