import Image from "next/image";
import { Heading, Text } from "@onyma-ds/react";

import { feedbacks, quotationMarksValues } from "./data";
import * as SC from "./styles";
import { FeedbackProps } from "./types";

export default function Feedback({ author }: FeedbackProps) {
  const feedbackPicked = feedbacks[author];

  return (
    <SC.Container id={`feedback-${author}`}>
      <Image
        src={quotationMarksValues.src}
        alt={quotationMarksValues.alt}
        width={quotationMarksValues.width}
        height={quotationMarksValues.height}
      />
      <Heading as="h3" type="heading_03">
        {`“${feedbackPicked.heading}”`}
      </Heading>
      <Text as="p">{`“${feedbackPicked.feedback}”`}</Text>
      <SC.AuthorBox>
        <Text type="body_02">{feedbackPicked.author}</Text>
        <Image
          src={feedbackPicked.image.src}
          alt={feedbackPicked.image.alt}
          width={feedbackPicked.image.width}
          height={feedbackPicked.image.height}
        />
      </SC.AuthorBox>
    </SC.Container>
  );
}
