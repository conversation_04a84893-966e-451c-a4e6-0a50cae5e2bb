import { renderTheme } from "@/utils/renderTheme";
import { screen } from "@testing-library/react";
import { Menu } from ".";

interface SutProps {
  isMenuOpen?: boolean;
}

const makeSut = ({ isMenuOpen = false }: SutProps = {}) => {
  renderTheme(<Menu isMenuOpen={isMenuOpen} />);
};

describe("<Menu />", () => {
  it("should not render menu when it's closed", () => {
    makeSut({ isMenuOpen: false });
    const menu = screen.getByTestId("header-menu-container");
    expect(menu).toHaveStyle("max-height: 0");
  });

  it("should render menu when it's open", () => {
    makeSut({ isMenuOpen: true });
    const menu = screen.getByTestId("header-menu-container");
    expect(menu).toHaveStyle("max-height: 300px");
  });
});
