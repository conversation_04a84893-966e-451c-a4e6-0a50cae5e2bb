import { Reducer } from "react";
import { applyMasks } from "./applyMasks";
import { Action, FormState } from "./types";

export const formReducer: Reducer<FormState, Action> = (
  currentState,
  action
): FormState => {
  const { field, value, checked } = applyMasks(action.payload);
  switch (action.type) {
    case "CHANGE_INPUT_TEXT":
      return {
        ...currentState,
        [field]: value,
      };
    case "CHANGE_SERVICES_OF_INTEREST":
      if (checked) {
        return {
          ...currentState,
          servicesOfInterest: [
            ...currentState.servicesOfInterest,
            value as string,
          ],
        };
      }
      return {
        ...currentState,
        servicesOfInterest: currentState.servicesOfInterest.filter(
          (service) => service !== value
        ),
      };
    default:
      return currentState;
  }
};
