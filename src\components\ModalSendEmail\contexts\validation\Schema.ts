import * as yup from "yup";

const requiredField = "Campo obrigatório";
const invalidEmail = "E-mail inválido";
const phoneInvalid = "Telefone inválido";

export const formSchema = yup.object({
  name: yup.string().required(requiredField),
  role: yup.string().required(requiredField),
  email: yup.string().email(invalidEmail).required(requiredField),
  phone: yup
    .string()
    .min(14, phoneInvalid)
    .max(15, phoneInvalid)
    .required(requiredField),
  cnpj: yup.string().length(18).required(requiredField),
  numberOfCollaborators: yup
    .number()
    .min(1, "O valor mínimo é 1")
    .required(requiredField),
  averageMonthlyAdmissions: yup
    .number()
    .min(1, "O valor mínimo é 1")
    .required(requiredField),
  averageMonthlyLayoffs: yup
    .number()
    .min(1, "O valor mínimo é 1")
    .required(requiredField),
  workModel: yup.string().required().required(requiredField),
  servicesOfInterest: yup.array().min(1, "Deve ter pelo menos 1 serviço"),
  message: yup.string(),
});
