import styled from "styled-components";

export const Container = styled.div`
  max-width: 540px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;

  & > h3 {
    color: ${({ theme }) => theme.colors.secondary};
  }

  & > p {
    color: ${({ theme }) => theme.colors.gray_60};
  }

  & > p,
  & > h3 {
    text-align: center;
  }
`;

export const AuthorBox = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;

  & > p {
    color: ${({ theme }) => theme.colors.gray_40};
    text-align: center;
  }

  & > svg {
    color: #fd9cbf;
  }
`;
