default:
  image: node:18

deploy_preview:
  stage: deploy
  except:
    - main
  script:
    - npm install --global vercel
    - vercel pull --yes --environment=preview --scope=onyma --token=$VERCEL_TOKEN
    - vercel build --scope=onyma --token=$VERCEL_TOKEN
    - vercel deploy --prebuilt  --scope=onyma --token=$VERCEL_TOKEN

deploy_production:
  stage: deploy
  only:
    - main
  script:
    - npm install --global vercel
    - vercel pull --yes --environment=production --scope=onyma --token=$VERCEL_TOKEN
    - vercel build --prod --scope=onyma --token=$VERCEL_TOKEN
    - vercel deploy --prebuilt --prod --scope=onyma --token=$VERCEL_TOKEN