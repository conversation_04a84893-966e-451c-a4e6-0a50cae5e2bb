import Image from "next/image";
import { LoadingThreeDots } from "./components";
import { MessageProps } from "./types";
import styles from "./styles.module.scss";

export default function Message({
  message,
  isTyping,
  messageCountParameter,
}: MessageProps) {
  const classByAuthor = message.author === "user" ? styles.user : styles.bot;
  const textOrLoading = isTyping ? (
    <LoadingThreeDots />
  ) : (
    <pre>{message.text}</pre>
  );

  return (
    <div className={`${styles.container} ${classByAuthor}`}>
      {message.author === "onyai" && (
        <div className={styles.profilePicture}>
          <Image
            src="/modules/onyAI/ony-logo-default.svg"
            width={20}
            height={16}
            alt="OnyAI"
          />
        </div>
      )}
      <div className={styles.baloon}>{textOrLoading}</div>
    </div>
  );
}
