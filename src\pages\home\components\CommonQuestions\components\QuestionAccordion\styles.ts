import styled from "styled-components";
import { Symbol } from "@onyma-ds/react";
import { colorHexToRgba } from "@/utils/colorHexToRgba";

export const Container = styled.div`
  width: 100%;
  padding: 1.5rem 0;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-bottom-color: ${({ theme }) =>
    colorHexToRgba(theme.colors.black, 0.08)};

  *::-webkit-scrollbar {
    width: 6px;
  }

  *::-webkit-scrollbar-track {
    background: transparent;
  }

  *::-webkit-scrollbar-thumb {
    background-color: ${({ theme }) => theme.colors.gray_60};
    border-radius: 20px;
    border: 3px solid ${({ theme }) => theme.colors.gray_60};
  }
`;

export const MainBox = styled.section`
  margin-bottom: 0.125rem;
  display: grid;
  grid-template-columns: 1fr 1.5rem;
  align-items: center;
  gap: 1rem;

  & > h5 {
    color: ${({ theme }) => theme.colors.tertiary_dark};
  }
`;

export const ButtonShow = styled.button.attrs({
  type: "button",
})`
  background-color: transparent;
  border-color: transparent;
`;

export const AddSymbol = styled(Symbol).attrs({
  name: "add",
  size: 22,
})`
  color: ${({ theme }) => theme.colors.black};
  display: block;

  &[aria-hidden="false"] {
    display: none;
  }
`;

export const RemoveSymbol = styled(Symbol).attrs({
  name: "remove",
  size: 22,
})`
  color: ${({ theme }) => theme.colors.black};
  display: block;

  &[aria-hidden="false"] {
    display: none;
  }
`;

export const ContentBox = styled.div`
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s linear;

  & > * {
    color: ${({ theme }) => theme.colors.gray_40};
  }

  &[aria-expanded="true"] {
    max-height: 200px;
  }
`;
