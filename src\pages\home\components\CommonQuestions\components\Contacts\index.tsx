import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { Button, Heading, Text } from "@onyma-ds/react";

import { ModalSendEmail } from "@/components";
import { svgsPaths } from "@/assets/paths/svgPaths";
import * as SC from "./styles";
import { generators } from "@/utils/generators";

export default function Contacts() {
  const [showModalSendEmail, setShowModalSendEmail] = useState(false);

  const handleOpenModalSendEmail = () => setShowModalSendEmail(true);

  return (
    <SC.Container id="contacts">
      <Heading as="h3" type="heading_03">
        Ficou com alguma dúvida?
      </Heading>
      <Text type="body_02">
        Escolha abaixo algum método de entrar em contato com a Onyma
      </Text>
      <SC.ButtonsBox>
        <Link href={generators.whatsAppLink()} target="_blank">
          <Button
            type="button"
            variant="success"
            className="button-whatsapp"
            title="Clique aqui para enviar uma mensagem no WhatsApp para a nossa equipe"
          >
            <Image
              src={svgsPaths.socialMedia.whatsAppWhite.path}
              alt="Logo do WhatsApp"
              width={20}
              height={20}
            />
            Enviar um WhatsApp
          </Button>
        </Link>
        <Button
          type="button"
          variant="quaternary"
          title="Clique aqui para enviar um E-mail para a nossa equipe"
          onClick={handleOpenModalSendEmail}
        >
          <SC.SymbolGeneric name="mail" />
          Formulário de contato
        </Button>
      </SC.ButtonsBox>

      {showModalSendEmail && (
        <ModalSendEmail
          show={showModalSendEmail}
          onClose={() => setShowModalSendEmail(false)}
        />
      )}
    </SC.Container>
  );
}
