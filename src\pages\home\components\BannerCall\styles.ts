import styled, { css } from "styled-components";

export const Container = styled.section`
  width: 100%;
  height: 428px;
  background: linear-gradient(254.28deg, #27b5bf 0%, #17a9fb 100%);
  padding: 8rem;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;

  & > image {
    object-fit: cover;
    width: 150%;
    height: 150%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
`;

export const Content = styled.div`
  max-width: 700px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  position: relative;
  z-index: 1;

  & > h2 {
    color: ${({ theme }) => theme.colors.white};
    text-align: center;
    max-width: 1184px;
  }

  & > a > button {
    color: ${({ theme }) => theme.colors.secondary};
  }
`;

export const ImageBox = styled.div`
  position: absolute;
  width: 150%;
  height: 150%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
`;

export const HeartBoxGreen = styled.section`
  position: absolute;
  left: 9%;
  bottom: 15%;
  width: 177px;
  height: 108.17px;
  color: #64fa0a;
  left: 9%;

  @media (max-width: 920px) {
    width: 100px;
    height: auto;
  }

  @media (max-width: 524px) {
    width: 60px;
    height: auto;
  }
`;

export const HeartBoxPurple = styled.section`
  position: absolute;
  right: 9%;
  bottom: 15%;
  width: 77px;
  height: 47.06px;
  color: #9339bf;

  @media (max-width: 920px) {
    width: 50px;
    height: auto;
  }

  @media (max-width: 524px) {
    width: 50px;
    height: auto;
  }
`;
