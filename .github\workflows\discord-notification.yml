# This is a basic workflow to help you get started with Actions

name: Discord Notification

# Controls when the action will run.
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  pull_request:
    branches:
      - main
      - develop
    types:
      - review_requested

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:
env:
  REVIEWER_ID: ${{(github.event.pull_request.requested_reviewers[0].login == 'CaiqueRibeiro' && '<@735527739892695231>') || (github.event.pull_request.requested_reviewers[0].login == 'gbelther' && '<@346495447592075265>') || (github.event.pull_request.requested_reviewers[0].login == 'lucsmachado' && '<@747454683811151972>')}}

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - name: Discord Notification
        uses: rjstone/discord-webhook-notify@v1
        if: ${{ env.REVIEWER_ID }}
        with:
          severity: info
          text: ":index_pointing_at_the_viewer: Você foi o escolhido para revisar a PR ${{ env.REVIEWER_ID }} \n [PR LINK](https://github.com/${{github.repository}}/pull/${{github.event.pull_request.number}})"
          webhookUrl: ${{ secrets.DISCORD_WEBHOOK }}
          description: " - **Repository** [${{github.repository}}](https://github.com/${{github.repository}}) \n  - **Branch**: ${{github.base_ref}}\n - **Author**: ${{github.event.pull_request.user.login}} \n"
