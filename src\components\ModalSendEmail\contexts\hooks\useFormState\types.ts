import { Dispatch } from "react";

type FormState = {
  name: string;
  role: string;
  email: string;
  phone: string;
  cnpj: string;
  numberOfCollaborators: number;
  averageMonthlyAdmissions: number;
  averageMonthlyLayoffs: number;
  workModel: "in-person" | "remote" | "hybrid";
  servicesOfInterest: string[];
  message: string;
};

type Action = {
  type: "CHANGE_INPUT_TEXT" | "CHANGE_SERVICES_OF_INTEREST";
  payload: {
    field: string;
    value: string | number;
    checked?: boolean;
  };
};

type FormDispatch = Dispatch<Action>;

export type { FormState, Action, FormDispatch };
