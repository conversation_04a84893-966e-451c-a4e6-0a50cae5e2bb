import axios from "axios";
import { RemoteSendEmailToCommercialTeam } from "./types";

export default async function remoteSendEmailToCommercialTeam(
  data: RemoteSendEmailToCommercialTeam.Params
) {
  try {
    await axios.request({
      method: "POST",
      url: `${process.env.NEXT_PUBLIC_WEBSITE_URL}/api/email-to-commercial-team`,
      data: data,
    });
  } catch (error) {
    const errorMessage = error?.response?.data?.error || "Erro desconhecido";
    throw new Error(errorMessage);
  }
}
