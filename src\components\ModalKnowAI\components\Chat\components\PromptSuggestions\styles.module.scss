$breakpoint: 540px;

.container {
  position: relative;
  display: grid;
  z-index: 1;
  position: relative;
  min-height: 2.125rem;
  user-select: none;

  &:before {
    content: "";
    width: 32px;
    height: 100%;
    background: linear-gradient(90deg, #090415 0%, rgba(9, 4, 22, 0) 100%);
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
  }

  &:after {
    content: "";
    width: 32px;
    height: 100%;
    background: linear-gradient(90deg, #090415 0%, rgba(9, 4, 22, 0) 100%);
    transform: matrix(-1, 0, 0, 1, 0, 0);
    position: absolute;
    top: 0;
    right: 0;
  }
}
