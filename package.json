{"name": "website-onyma", "version": "1.2.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint", "test": "jest --passWithNoTests --no-cache --runInBand --detectOpenHandles", "test:watch": "yarn test -- --watch", "test:ci": "yarn test -- --coverage", "test:staged": "yarn test -- --findRelatedTests", "husky:prepare": "husky install"}, "dependencies": {"@onyma-ds/react": "^1.5.0", "@onyma-ds/tokens": "^1.0.0", "@types/node": "18.11.18", "@types/react": "18.0.27", "@types/react-dom": "18.0.10", "amqplib": "^0.10.3", "axios": "^1.3.4", "eslint": "8.33.0", "eslint-config-next": "13.1.6", "next": "^13.4.4", "next-seo": "^5.15.0", "next-sitemap": "^4.0.5", "react": "18.2.0", "react-dom": "18.2.0", "socket.io-client": "^4.6.2", "styled-components": "^5.3.6", "typescript": "4.9.5", "yup": "^1.0.0"}, "devDependencies": {"@faker-js/faker": "^7.6.0", "@svgr/webpack": "^6.5.1", "@testing-library/dom": "^8.20.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/amqplib": "^0.10.1", "@types/styled-components": "^5.1.26", "git-commit-msg-linter": "^4.8.3", "husky": "^8.0.3", "jest": "^29.4.1", "jest-environment-jsdom": "^29.4.1", "jest-styled-components": "^7.1.1", "lint-staged": "^13.1.2", "sass": "^1.62.1", "ts-jest": "^29.0.5", "ts-node": "^10.9.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}