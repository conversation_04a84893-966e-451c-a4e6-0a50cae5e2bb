import { ReactNode } from "react";
import { RemoteSendEmailToCommercialTeam } from "@/api/remoteSendEmailToCommercialTeam/types";
import { RemoteOnyAISignIn, RemoteOnyAISignUp } from "@/api/onyAI";

type ApiContextData = {
  remoteSendEmailToCommercialTeam: (
    params: RemoteSendEmailToCommercialTeam.Params
  ) => Promise<void>;
  onyAI: {
    signUp: (
      params: RemoteOnyAISignUp.Params
    ) => Promise<RemoteOnyAISignUp.Result>;
    signIn: (
      params: RemoteOnyAISignIn.Params
    ) => Promise<RemoteOnyAISignIn.Result>;
  };
};

type ApiContextProviderProps = {
  children: ReactNode;
};

export type { ApiContextData, ApiContextProviderProps };
