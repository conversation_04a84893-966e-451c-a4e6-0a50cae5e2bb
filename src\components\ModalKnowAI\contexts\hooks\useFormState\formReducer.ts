import { Reducer } from "react";
import { applyMasks } from "./applyMasks";
import { Action, FormState } from "./types";

export const formReducer: Reducer<FormState, Action> = (
  currentState,
  action
): FormState => {
  const { field, value } = applyMasks(action.payload);
  switch (action.type) {
    case "CHANGE_INPUT_TEXT": {
      if (!field) return currentState;
      const valueParsed =
        field === "numberOfEmployees"
          ? Number(value.replace(/^0+/, "").replace(/\D/gi, ""))
          : value;
      return {
        ...currentState,
        [field]: valueParsed,
      };
    }
    default:
      return currentState;
  }
};
