import { Modal } from "@onyma-ds/react";

import { customStylesContent } from "./customStyles";
import { ModalBody } from "./styles";
import { ModalSendEmailProps } from "./types";
import { useEffect } from "react";

const FORM_URL =
  "https://webforms.pipedrive.com/f/oI2f4SWPlCGhrXvd8n0poveSQBUKoc0dytn64gcgH0FX5fHb61u7GG8flqiVc9Z";
const LOADER_SRC = "https://webforms.pipedrive.com/f/loader";

export function PipedriveWebForm() {
  useEffect(() => {
    if (typeof window === "undefined") return;
    if (!document.querySelector(`script[src="${LOADER_SRC}"]`)) {
      const s = document.createElement("script");
      s.src = LOADER_SRC;
      s.async = true;
      document.body.appendChild(s);
    }
  }, []);

  return <div className="pipedriveWebForms" data-pd-webforms={FORM_URL} />;
}

export default function ModalSendEmail({ show, onClose }: ModalSendEmailProps) {
  if (!show) return <></>;

  return (
    <Modal
      id="modal-send-email"
      show={show}
      onClose={onClose}
      stylesContent={customStylesContent}
    >
      <ModalBody>
        <PipedriveWebForm />
      </ModalBody>
    </Modal>
  );
}
