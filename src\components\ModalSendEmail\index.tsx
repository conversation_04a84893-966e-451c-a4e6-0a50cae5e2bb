import { Modal } from "@onyma-ds/react";

import { PipedriveWebForm } from "../PipedriveForm";
import { customStylesContent } from "./customStyles";
import { ModalBody } from "./styles";
import { ModalSendEmailProps } from "./types";

export default function ModalSendEmail({ show, onClose }: ModalSendEmailProps) {
  if (!show) return <></>;

  return (
    <Modal
      id="modal-send-email"
      show={show}
      onClose={onClose}
      stylesContent={customStylesContent}
    >
      <ModalBody>
        <PipedriveWebForm />
      </ModalBody>
    </Modal>
  );
}
