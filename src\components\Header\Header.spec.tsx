import { renderTheme } from "@/utils/renderTheme";
import { screen, fireEvent } from "@testing-library/react";
import { Header } from ".";

jest.mock("next/router", () => {
  return {
    useRouter: () => ({
      pathname: "/",
    }),
  };
});

const makeSut = () => {
  renderTheme(<Header />);
};

describe("<Header />", () => {
  it("should not render background filter when menu is closed", () => {
    makeSut();
    const backgroundFilter = screen.queryByTestId("header-background-filter");
    expect(backgroundFilter).toBeFalsy();
  });

  it("should render background filter when menu is open", () => {
    makeSut();
    const hamburgerButton = screen.getByTitle("Abrir menu");
    fireEvent.click(hamburgerButton);
    const backgroundFilter = screen.queryByTestId("header-background-filter");
    expect(backgroundFilter).toBeTruthy();
  });

  it("should not render menu when it's closed", () => {
    makeSut();
    const menu = screen.getByTestId("header-menu-container");
    expect(menu).toHaveStyle("max-height: 0");
  });

  it("should render menu when hamburger button is clicked", () => {
    makeSut();
    const hamburgerButton = screen.getByTitle("Abrir menu");
    fireEvent.click(hamburgerButton);
    const menu = screen.getByTestId("header-menu-container");
    expect(menu).toHaveStyle("max-height: 300px");
  });

  it("should not render menu when close button is clicked", () => {
    makeSut();
    const hamburgerButton = screen.getByTitle("Abrir menu");
    fireEvent.click(hamburgerButton);
    fireEvent.click(hamburgerButton);
    const menu = screen.getByTestId("header-menu-container");
    expect(menu).toHaveStyle("max-height: 0");
  });

  it("should not render background filter when it's clicked", () => {
    makeSut();
    const hamburgerButton = screen.getByTitle("Abrir menu");
    fireEvent.click(hamburgerButton);
    const backgroundFilter = screen.getByTestId("header-background-filter");
    fireEvent.click(backgroundFilter);
    const backgroundFilterAfterClick = screen.queryByTestId(
      "header-background-filter"
    );
    expect(backgroundFilterAfterClick).toBeFalsy();
  });

  it("should close menu when background filter is clicked", () => {
    makeSut();
    const hamburgerButton = screen.getByTitle("Abrir menu");
    fireEvent.click(hamburgerButton);
    const backgroundFilter = screen.getByTestId("header-background-filter");
    fireEvent.click(backgroundFilter);
    const menu = screen.getByTestId("header-menu-container");
    expect(menu).toHaveStyle("max-height: 0");
  });

  it("should render hamburger icon when menu is closed", () => {
    makeSut();
    const hamburgerIcon = screen.queryByTestId("hamburger-icon");
    expect(hamburgerIcon).toBeTruthy();
  });

  it("should render close icon when menu is open", () => {
    makeSut();
    const hamburgerButton = screen.getByTitle("Abrir menu");
    fireEvent.click(hamburgerButton);
    const closeIcon = screen.queryByTestId("close-icon");
    expect(closeIcon).toBeTruthy();
  });
});
