import Image from "next/image";
import Link from "next/link";
import { generators } from "@/utils/generators";
import { svgsPaths } from "@/assets/paths/svgPaths";
import * as SC from "./styles";

export const LinkQuotationButton = ({ onClose }: { onClose: () => void }) => {
  return (
    <SC.Container>
      {/* <Link
        id="link-button-quote"
        href={process.env.NEXT_PUBLIC_LANDING_PAGE_URL || ""}
        target="_blank"
        onClick={onClose}
      >
        <SC.ButtonQuote
          type="button"
          variant="secondary"
          title="Simule sua cotação clicando aqui"
        >
          Faça a sua cotação
        </SC.ButtonQuote>
      </Link> */}

      <Link
        id="link-button-quote"
        href={generators.whatsAppLink()}
        target="_blank"
        onClick={onClose}
      >
        <SC.WhatsAppButton>
          <SC.ImageBox>
            <Image
              src={svgsPaths.socialMedia.whatsAppWhite.path}
              alt="Logo do WhatsApp"
              fill
            />
          </SC.ImageBox>
        </SC.WhatsAppButton>
      </Link>
    </SC.Container>
  );
};
