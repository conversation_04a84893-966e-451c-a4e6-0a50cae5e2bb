export default function phoneMask(value: string) {
  if (!value) return "";
  return value
    .replace(/\D/g, "") // substitui qualquer caracter que nao seja numero por nada
    .replace(/^(\d{2})(\d)/, "($1) $2") // captura 2 grupos de numero o primeiro de 2 e o segundo de 1, apos capturar o primeiro grupo ele adiciona um parenteses antes do segundo grupo de numero e antes do primeiro grupo
    .replace(/(\d{4})(\d)/, "$1-$2")
    .replace(/(-\d{4})/, "$1")
    .replace(/(\d{4})-(\d{1})(\d{4})/, "$1$2-$3")
    .replace(/(-\d{4})\d+?$/, "$1");
}
