import { useEffect, useRef } from "react";
import { Message } from "./components";
import { MessagesProps } from "./types";
import styles from "./styles.module.scss";

export default function Messages({ messages }: MessagesProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  const userMessages = messages.filter((m) => m.author === "user");

  useEffect(() => {
    if (containerRef && containerRef.current) {
      containerRef.current.scroll({ top: containerRef.current.scrollHeight });
    }
  }, [messages, containerRef]);

  const lastMessage = messages.at(-1);

  return (
    <div ref={containerRef} className={styles.container}>
      <div>
        {messages.map((message) => (
          <Message key={message.id} message={message} />
        ))}
        {lastMessage?.author === "user" && (
          <Message
            message={{ id: "loading", author: "onyai", text: "" }}
            isTyping
          />
        )}
      </div>
    </div>
  );
}
