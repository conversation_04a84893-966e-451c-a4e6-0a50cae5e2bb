import { Symbol } from "@onyma-ds/react";
import styled from "styled-components";

export const Container = styled.div`
  margin-top: 8rem;
  margin-bottom: 1rem;
  flex-direction: column;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;

  & > h3 {
    color: ${({ theme }) => theme.colors.tertiary};
    text-align: center;
  }

  & > p {
    color: ${({ theme }) => theme.colors.gray_60};
    text-align: center;
  }
`;

export const ButtonsBox = styled.section`
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;

  @media (max-width: 512px) {
    flex-direction: column;

    & > button,
    & > a,
    & > a > button {
      width: 100%;
    }
  }

  & > button,
  & > a > button {
    max-width: 250px;
    color: ${({ theme }) => theme.colors.white};
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;

    &.button-whatsapp {
      background-color: #25d366;
    }
  }
`;

export const SymbolGeneric = styled(Symbol).attrs({
  size: 20,
})``;
