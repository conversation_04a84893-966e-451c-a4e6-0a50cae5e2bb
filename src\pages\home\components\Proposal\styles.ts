import { Button } from "@onyma-ds/react";
import styled, { css } from "styled-components";

export const Content = styled.div`
  width: 100%;
  background: linear-gradient(254.28deg, #27b5bf 0%, #17a9fb 100%);
  color: ${({ theme }) => theme.colors.white};
  text-align: center;
  padding: ${({ theme }) => theme.spacings.xxxl};
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;

  @media (max-width: 490px) {
    padding: ${({ theme }) => `${theme.spacings.xxxl} ${theme.spacings.sm}`};
  }
`;

export const ActionButtons = styled.div`
  margin-top: 1rem;
  display: flex;
  justify-content: center;
  gap: 1.5rem;
`;

export const ButtonSimulation = styled(Button).attrs({
  type: "button",
  variant: "white",
})`
  color: ${({ theme }) => theme.colors.secondary};
`;

export const ButtonToContact = styled(Button).attrs({
  type: "button",
  variant: "white",
})`
  color: ${({ theme }) => theme.colors.white};

  &:hover {
    color: ${({ theme }) => theme.colors.secondary};
  }

  &:focus {
    border-color: ${({ theme }) => theme.colors.white};
  }
`;

export const DashboardRHImage = styled.div`
  position: absolute;
  top: 100%;
  max-width: 1000px;
  width: 90%;
  aspect-ratio: 1.8;
  transform: translateY(-50%);
`;

type ContainerProps = {
  extraPadding: number;
};

export const Container = styled.div<ContainerProps>`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;

  ${({ theme, extraPadding }) =>
    extraPadding > 0 &&
    css`
      margin-bottom: ${extraPadding}px;

      ${Content} {
        padding-bottom: calc(${theme.spacings.xxxl} + ${extraPadding / 16}rem);
      }
    `};
`;
