import { renderTheme } from "@/utils/renderTheme";
import { fireEvent, screen } from "@testing-library/react";
import EmailSent from ".";

describe("<EmailSent />", () => {
  it("should call onClose correctly", () => {
    const onCloseMock = jest.fn();
    renderTheme(<EmailSent onClose={onCloseMock} />);
    const submitButton = screen.getByTitle("Vou aguardar contato");
    fireEvent.click(submitButton);
    expect(onCloseMock).toHaveBeenCalled();
  });
});
