.container {
  width: min(90vw, 720px);
  padding: 4rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.modalHeader {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;

  & > h2 {
    font-size: 2rem;
    font-family: Caveat;
    color: #fade14;
    margin-bottom: 0.5rem;
  }

  & > h1 {
    font-size: 2.5rem;
    font-weight: 600;
    line-height: 3rem;
    text-align: center;

    & > span {
      position: relative;

      span {
        position: relative;
        z-index: 1;
      }

      img {
        z-index: 0;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        transform: scale(1.2);
      }
    }
  }
}

.modalBody {
  width: 100%;

  & > p {
    font-size: 1.25rem;
    font-weight: 400;
    line-height: 140%;
    text-align: center;
  }
}

.modalFooter {
  width: 100%;
  margin-top: 2.5rem;

  & > button {
    width: 100%;
    background-color: #7159eb;
    border: 1px solid #7159eb;
    border-radius: 4px;
    font-family: "Roboto", sans-serif;
    font-size: 1rem;
    font-weight: 600;
    line-height: 140%;
    color: #fff;
    padding: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;

    &:disabled {
      background-color: #cccccc;
      border-color: #cccccc;
      color: #999999;
    }
  }
}
