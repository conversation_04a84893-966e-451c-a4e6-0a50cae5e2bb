import { svgsPaths } from "@/assets/paths/svgPaths";

export const quotationMarksValues = {
  src: svgsPaths.general.quotationMasksSecondaryDark.path,
  alt: "Ícone de aspas duplas indicando uma citação",
  width: 68,
  height: 48,
};

export const feedbacks = {
  meliuzJuliana: {
    image: {
      src: svgsPaths.ourClients.meliuzTextOriginal.path,
      alt: "Ícone da Meliuz",
      width: 85.71,
      height: 24,
    },
    author: "<PERSON>",
    heading: "Plataforma flexível e clean",
    feedback:
      "Plataforma flexível e clean, tudo bem intuitivo que atende as necessidades do cliente. Todas as demandas solicitadas são atendidas a tempo. Bom suporte técnico, sempre pronto a atender.",
  },
  pipoSaudePriscilla: {
    image: {
      src: svgsPaths.ourClients.pipoSaudeTextOriginal.path,
      alt: "<PERSON><PERSON><PERSON> <PERSON>",
      width: 114,
      height: 24,
    },
    author: "<PERSON><PERSON><PERSON>",
    heading: "Agilidade nos agendamentos",
    feedback:
      "Antes da Onyma o maior problema encontrado era na hora do agendamento, não ter a opção de ter tudo centralizado, tínhamos que buscar as clínicas em cada município mais próximo à residência do colaborador, era demorado e moroso. Os maiores benefícios são agilidade nos agendamentos e informações centralizadas em uma única plataforma. Vejo muito valor agregado tanto para o RH quanto para a experiência do colaborador.",
  },
};
