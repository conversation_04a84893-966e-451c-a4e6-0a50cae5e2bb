import { UIEvent, useEffect, useRef, useState } from "react";
import Image from "next/image";
import { Heading } from "@onyma-ds/react";

import * as SC from "./styles";
import { ourClients } from "./data";

export default function OurClients() {
  const itemsRef = useRef<HTMLDivElement>(null);

  const [elementWidths, setElementWidths] = useState({
    widthWithScroll: 0,
    widthWithoutScroll: 0,
  });
  const [dotsAmount, setDotsAmount] = useState(0);
  const [currentDot, setCurrentDot] = useState(1);

  const handleScrollByClick = (newDot: number) => {
    if (newDot === currentDot) return;
    const scrollViewLeft = elementWidths.widthWithoutScroll * (newDot - 1);
    itemsRef.current?.scroll({ left: scrollViewLeft, behavior: "smooth" });
  };

  const handleItemsScroll = (
    event: UIEvent<HTMLDivElement, globalThis.UIEvent>
  ) => {
    const { scrollLeft } = event.currentTarget;
    const currentDotTemp =
      Math.ceil(scrollLeft / elementWidths.widthWithoutScroll) + 1;
    setCurrentDot(currentDotTemp);
  };

  const calculateElementWidths = () => {
    const box = itemsRef.current?.getBoundingClientRect();
    const widthWithScroll = itemsRef.current?.scrollWidth;

    if (box && widthWithScroll) {
      const widthWithScroll = itemsRef.current?.scrollWidth;
      const widthWithoutScroll = box.width;
      setElementWidths({
        widthWithoutScroll: Math.round(widthWithoutScroll),
        widthWithScroll,
      });
      const dotsAmountTemp = widthWithScroll / widthWithoutScroll;
      const dotsAmountTempRounded = Math.ceil(
        Number(dotsAmountTemp.toFixed(3))
      );
      setDotsAmount(dotsAmountTempRounded);
    }
  };

  useEffect(() => {
    window.addEventListener("resize", calculateElementWidths);
    return () => window.removeEventListener("resize", calculateElementWidths);
  }, [itemsRef]);

  useEffect(() => calculateElementWidths(), []);

  const dotsArrayToRender = [...Array(dotsAmount || 0)].map((_, i) => i + 1);

  return (
    <SC.Container id="nossos-clientes" suppressHydrationWarning={true}>
      <Heading as="h2" type="heading_02">
        Empresas que confiam na Onyma
      </Heading>
      <SC.Slider>
        <SC.Items ref={itemsRef} onScroll={handleItemsScroll}>
          {ourClients.map((client) => (
            <Image
              key={client.src}
              id={client.alt}
              src={client.src}
              alt={client.alt}
              width={client.width}
              height={client.height}
            />
          ))}
        </SC.Items>
        <SC.DotsScroll id="our-clients-scroll">
          {dotsArrayToRender.length > 0 &&
            dotsArrayToRender.map((dot) => (
              <SC.Dot
                key={`dot-${dot}`}
                current={dot === currentDot}
                onClick={() => handleScrollByClick(dot)}
              />
            ))}
        </SC.DotsScroll>
      </SC.Slider>
    </SC.Container>
  );
}
