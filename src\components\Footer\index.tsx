import Image from "next/image";
import Link from "next/link";
import { Heading, Text } from "@onyma-ds/react";

import { svgsPaths } from "@/assets/paths/svgPaths";
import * as SC from "./styles";

export default function Footer() {
  return (
    <SC.Container>
      <SC.OnymaLove>
        <Text type="body_02">
          Feito com
          <Image
            src={svgsPaths.onyma.onymaHeartPurple.path}
            width={18}
            height={11}
            alt="Coração Onyma"
          />
          pela Onyma
        </Text>
        <Link
          href="https://www.linkedin.com/company/onymadigital"
          target="_blank"
          title="Abrir Linkedin da Onyma"
        >
          <Image
            src={svgsPaths.socialMedia.linkedinGray60.path}
            width={24}
            height={24}
            alt="Logo do Linkedin"
          />
        </Link>
      </SC.OnymaLove>
      <SC.Redirects>
        <Link href="/quem-somos" target="_blank">
          <SC.LinkText>Quem somos?</SC.LinkText>
          <SC.OutwardSymbol />
        </Link>
        <Link href="/termos-de-uso" target="_blank">
          <SC.LinkText>Termos de uso</SC.LinkText>
          <SC.OutwardSymbol />
        </Link>
        <Link href="/documents/politica-de-privacidade.pdf" target="_blank">
          <SC.LinkText>Política de privacidade</SC.LinkText>
          <SC.OutwardSymbol />
        </Link>
      </SC.Redirects>
      <SC.Localization>
        <Text type="body_03">
          Onyma Digital Ltda. — CNPJ 38.318.450/0001-08 — Avenida Paulista, 453,
          12º andar, conjunto 122, Bela Vista, São Paulo, SP, 01311-000
        </Text>
        <Heading as="h6" type="heading_06">
          SSL Secure{" "}
          <span>
            <Image
              src={svgsPaths.general.lockGray60.path}
              width={20}
              height={20}
              alt="Logo indicando site seguro"
            />
          </span>
        </Heading>
      </SC.Localization>
    </SC.Container>
  );
}
