import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { Links, Menu, LinkQuotationButton } from "./components";
import { svgsPaths } from "@/assets/paths/svgPaths";

import * as SC from "./styles";

export const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleIsMenuOpen = () => setIsMenuOpen((prevState) => !prevState);

  return (
    <>
      {isMenuOpen && (
        <SC.Background
          data-testid="header-background-filter"
          onClick={() => setIsMenuOpen(false)}
        />
      )}

      <SC.Container>
        <Menu isMenuOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

        <Link href="/" title="Navegar para a página inicial">
          <Image
            src={svgsPaths.onyma.logoTextPrimary.path}
            alt="Onyma"
            width={124}
            height={28}
          />
        </Link>

        <SC.Navbar>
          <SC.ButtonMenu
            type="button"
            title="Abrir menu"
            onClick={toggleIsMenuOpen}
          >
            {isMenuOpen ? (
              <SC.CloseSymbol data-testid="close-icon" />
            ) : (
              <SC.MenuSymbol data-testid="hamburger-icon" />
            )}
          </SC.ButtonMenu>

          <Links onClose={() => setIsMenuOpen(false)} />
        </SC.Navbar>

        <SC.LinkButtons>
          <LinkQuotationButton onClose={() => setIsMenuOpen(false)} />
        </SC.LinkButtons>
      </SC.Container>
    </>
  );
};
