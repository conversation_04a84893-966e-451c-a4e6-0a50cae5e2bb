import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON>, Head<PERSON> } from "@onyma-ds/react";

import OnymaHeart from "@/../public/svgs/onyma/onyma-heart-current-color.svg";
import { svgsPaths } from "@/assets/paths/svgPaths";
import * as SC from "./styles";

export default function BannerCall() {
  return (
    <SC.Container id="banner-call">
      <SC.Content>
        <Heading as="h2" type="heading_01">
          Vamos digitalizar a saúde ocupacional da sua empresa?
        </Heading>
        <Link
          id="banner-call-to-go-landing-page"
          href={process.env.NEXT_PUBLIC_LANDING_PAGE_URL || ""}
          target="_blank"
        >
          <Button type="button" variant="white">
            Clique aqui para começar
          </Button>
        </Link>
      </SC.Content>
      <SC.ImageBox>
        <Image
          src={svgsPaths.onyma.onymaHeartContour.path}
          fill
          alt="<PERSON><PERSON><PERSON> da Onyma Heart"
        />
      </SC.ImageBox>
      <SC.HeartBoxGreen>
        <OnymaHeart />
      </SC.HeartBoxGreen>
      <SC.HeartBoxPurple>
        <OnymaHeart />
      </SC.HeartBoxPurple>
    </SC.Container>
  );
}
