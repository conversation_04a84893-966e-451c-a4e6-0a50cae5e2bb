.container {
  color: #fff;
  width: 100%;
  padding: 1.5rem 3rem;
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  gap: 1rem;
  position: relative;

  @media (max-width: 720px) {
    grid-template-columns: 1fr;
  }

  & > * {
    position: relative;
    z-index: 1;
  }

  & > h5 {
    font-size: 1rem;

    span {
      font-size: 1.375rem;
    }
  }

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("./image.png");
    background-size: cover;
    filter: brightness(0.6);
  }
}

.actionButton {
  color: #fff;
  background-color: #7159eb;
  border: 1px solid #7159eb;
  border-radius: 4px;
  padding: 0.75rem;
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 140%;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}
