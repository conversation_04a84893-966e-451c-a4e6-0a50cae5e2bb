import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { Symbol } from "@onyma-ds/react";
import { ModalKnowAI } from "@/components";
import styles from "./styles.module.scss";

export default function HeaderKnowAI() {
  const router = useRouter();
  const [showModalKnowAI, setShowModalKnowAI] = useState(false);

  useEffect(() => {
    const pathsToNotOpenModalKnowAI = [
      "/politica-de-privacidade",
      "/termos-de-uso",
      "/404",
    ];
    const isInSomePaths = pathsToNotOpenModalKnowAI.some((path) =>
      router.pathname.includes(path)
    );
    if (!isInSomePaths) setShowModalKnowAI(true);
  }, [router]);

  return (
    <>
      {showModalKnowAI && (
        <ModalKnowAI
          show={showModalKnowAI}
          onClose={() => setShowModalKnowAI(false)}
        />
      )}
      <div className={styles.container}>
        <h5>
          <span>🤩</span> Novidade! Conheça a OnyAI - Um chat baseado em
          inteligência artificial para você tirar todas as suas dúvidas sobre
          SST!
        </h5>
        <button
          type="button"
          className={styles.actionButton}
          onClick={() => setShowModalKnowAI(true)}
        >
          Clique aqui para usar! <Symbol name="chevron_right" size={16} />
        </button>
      </div>
    </>
  );
}
