import styled, { css } from "styled-components";

export const Container = styled.div`
  max-width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;

  & > h2 {
    color: ${({ theme }) => theme.colors.primary};
    text-align: center;
  }

  * {
    ::-webkit-scrollbar {
      display: none;
    }

    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
`;

export const Slider = styled.div`
  width: 100%;
  max-width: 100%;
  padding: 0 2rem;
  display: flex;
  flex-direction: column;
`;

export const Items = styled.div`
  width: 100%;
  flex: 1;
  display: flex;
  gap: 2rem;
  overflow-x: scroll;
`;

export const DotsScroll = styled.div`
  margin-top: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
`;

type DotProps = {
  current: boolean;
};

export const Dot = styled.div<DotProps>`
  width: 12px;
  aspect-ratio: 1;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.colors.gray_90};
  cursor: pointer;

  ${({ current }) =>
    current &&
    css`
      background-color: ${({ theme }) => theme.colors.primary};
    `};
`;
