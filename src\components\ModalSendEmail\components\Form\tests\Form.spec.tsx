import { cleanup, fireEvent, screen, waitFor } from "@testing-library/react";
import { faker } from "@faker-js/faker";

import Form from "..";
import { renderTheme } from "@/utils/renderTheme";
import { FormValidation } from "@/components/ModalSendEmail/contexts/validation";
import FormContextProvider, {
  FormContext,
} from "../../../contexts/FormContext";
import * as Helpers from "./utils";
import { ToastContext } from "@onyma-ds/react";
import { ApiContext } from "@/contexts/ApiContext";

type SutParams = {
  errors?: FormValidation;
};

type SutTypes = {
  onHideFormMock: jest.Mock;
  onSubmitMock: jest.Mock;
  formDispatchMock: jest.Mock;
  addToastMock: jest.Mock;
  remoteSendEmailToCommercialTeamMock: jest.Mock;
};

const makeSut = ({ errors = {} }: SutParams = {}): SutTypes => {
  const onHideFormMock = jest.fn();
  const onSubmitMock = jest.fn();
  const addToastMock = jest.fn();
  const formDispatchMock = jest.fn();
  const remoteSendEmailToCommercialTeamMock = jest.fn();
  renderTheme(
    <ToastContext.Provider value={{ addToast: addToastMock }}>
      <ApiContext.Provider
        value={{
          remoteSendEmailToCommercialTeam: remoteSendEmailToCommercialTeamMock,
        }}
      >
        <FormContext.Provider
          value={{
            formState: Helpers.initialFormState,
            errors,
            formDispatch: formDispatchMock,
            onSubmit: onSubmitMock,
          }}
        >
          <Form onHideForm={onHideFormMock} />
        </FormContext.Provider>
      </ApiContext.Provider>
    </ToastContext.Provider>
  );
  return {
    onHideFormMock,
    onSubmitMock,
    formDispatchMock,
    addToastMock,
    remoteSendEmailToCommercialTeamMock,
  };
};

describe("<Form />", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cleanup();
  });

  describe("Initial values", () => {
    it("should render Input Name with correct value on render", () => {
      makeSut();
      Helpers.testValueByPlaceholder("Digite seu nome");
    });

    it("should render Input Role with correct value on render", () => {
      makeSut();
      Helpers.testValueByPlaceholder("Digite seu cargo");
    });

    it("should render Input Email with correct value on render", () => {
      makeSut();
      Helpers.testValueByPlaceholder("Digite seu e-mail corporativo");
    });

    it("should render Input Phone with correct value on render", () => {
      makeSut();
      Helpers.testValueByPlaceholder("(00) 00000-0000");
    });

    it("should render Input NumberOfCollaborators with correct value on render", () => {
      makeSut();
      Helpers.testValueByPlaceholder("Informe o número de colaboradores", "1");
    });

    it("should render Input AverageMonthlyAdmissions with correct value on render", () => {
      makeSut();
      Helpers.testValueByPlaceholder(
        "Informe a média de admissões mensais",
        "1"
      );
    });

    it("should render Input AverageMonthlyLayoffs with correct value on render", () => {
      makeSut();
      Helpers.testValueByPlaceholder(
        "Informe a média de demissões mensais",
        "1"
      );
    });

    it("should render Input Message with correct value on render", () => {
      makeSut();
      const message = screen.getByPlaceholderText("Digite aqui sua mensagem");
      expect(message).toHaveDisplayValue("");
    });

    describe("Work Model", () => {
      it("should render Input WorkModel(In Person) with correct value on render", () => {
        makeSut();
        Helpers.testCheckedByLabel(
          "Opção: modelo de trabalho presencial",
          true
        );
      });

      it("should render Input WorkModel(Remote) with correct value on render", () => {
        makeSut();
        Helpers.testCheckedByLabel("Opção: modelo de trabalho remoto", false);
      });

      it("should render Input WorkModel(Hybrid) with correct value on render", () => {
        makeSut();
        Helpers.testCheckedByLabel("Opção: modelo de trabalho híbrido", false);
      });
    });

    describe("Interest", () => {
      it("should render Input Interest(Occupational Health) with correct value on render", () => {
        makeSut();
        Helpers.testCheckedByLabel(
          "Opção: interesse no serviço de saúde ocupacional"
        );
      });

      it("should render Input Interest(eSocial) with correct value on render", () => {
        makeSut();
        Helpers.testCheckedByLabel("Opção: interesse no serviço de eSocial");
      });

      it("should render Input Interest(LTCAT) with correct value on render", () => {
        makeSut();
        Helpers.testCheckedByLabel("Opção: interesse no serviço de LTCAT");
      });

      it("should render Input Interest(CIPA) with correct value on render", () => {
        makeSut();
        Helpers.testCheckedByLabel("Opção: interesse no serviço de CIPA");
      });

      it("should render Input Interest(SESMT) with correct value on render", () => {
        makeSut();
        Helpers.testCheckedByLabel("Opção: interesse no serviço de SESMT");
      });
    });
  });

  describe("Validation", () => {
    it("should render Input Name with correct validation", async () => {
      const error = faker.hacker.phrase();
      makeSut({ errors: { name: { error: error } } });
      Helpers.submitForm();
      Helpers.testTextIsInTheScreen(error);
    });

    it("should render Input Role with correct validation", async () => {
      const error = faker.hacker.phrase();
      makeSut({ errors: { role: { error: error } } });
      Helpers.submitForm();
      Helpers.testTextIsInTheScreen(error);
    });

    it("should render Input Email with correct validation", async () => {
      const error = faker.internet.email();
      makeSut({ errors: { email: { error: error } } });
      Helpers.submitForm();
      Helpers.testTextIsInTheScreen(error);
    });

    it("should render Input Phone with correct validation", async () => {
      const error = faker.phone.number();
      makeSut({ errors: { phone: { error: error } } });
      Helpers.submitForm();
      Helpers.testTextIsInTheScreen(error);
    });

    it("should render Input CNPJ with correct validation", async () => {
      const error = faker.datatype.number().toString();
      makeSut({ errors: { cnpj: { error: error } } });
      Helpers.submitForm();
      Helpers.testTextIsInTheScreen(error);
    });

    it("should render Input NumberOfCollaborators with correct validation", async () => {
      const error = faker.datatype.number().toString();
      makeSut({ errors: { numberOfCollaborators: { error: error } } });
      Helpers.submitForm();
      Helpers.testTextIsInTheScreen(error);
    });

    it("should render Input AverageMonthlyAdmissions with correct validation", async () => {
      const error = faker.datatype.number().toString();
      makeSut({ errors: { averageMonthlyAdmissions: { error: error } } });
      Helpers.submitForm();
      Helpers.testTextIsInTheScreen(error);
    });

    it("should render Input WorkModel with correct validation", async () => {
      const error = faker.hacker.phrase();
      makeSut({ errors: { workModel: { error: error } } });
      Helpers.submitForm();
      Helpers.testTextIsInTheScreen(error);
    });

    it("should render Input ServicesOfInterest with correct validation", async () => {
      const error = faker.hacker.phrase();
      makeSut({ errors: { servicesOfInterest: { error: error } } });
      Helpers.submitForm();
      Helpers.testTextIsInTheScreen(error);
    });

    it("should render Input Message with correct validation", async () => {
      const error = faker.hacker.phrase();
      makeSut({ errors: { message: { error: error } } });
      Helpers.submitForm();
      Helpers.testTextIsInTheScreen(error);
    });
  });

  describe("Component Dynamics", () => {
    it("should call onSubmit on submit", async () => {
      const { onSubmitMock } = makeSut();
      Helpers.submitForm();
      expect(onSubmitMock).toHaveBeenCalled();
    });

    it("should call formDispatch when change form", async () => {
      const { formDispatchMock } = makeSut();
      const value = faker.name.fullName();
      Helpers.typeInputByPlaceholder("Digite seu nome", value);
      expect(formDispatchMock).toHaveBeenCalledWith({
        type: "CHANGE_INPUT_TEXT",
        payload: { field: "name", value },
      });
    });

    it("should call formDispatch(ServicesOfInterest) when change these fields", async () => {
      const { formDispatchMock } = makeSut();
      Helpers.checkOptionByLabel("Opção: modelo de trabalho remoto");
      expect(formDispatchMock).toHaveBeenCalledWith({
        type: "CHANGE_INPUT_TEXT",
        payload: { field: "workModel", value: "remote" },
      });
    });

    it("should call formDispatch(WorkModel) when change these fields", async () => {
      const { formDispatchMock } = makeSut();
      Helpers.checkOptionByLabel("Opção: interesse no serviço de LTCAT");
      expect(formDispatchMock).toHaveBeenCalledWith({
        type: "CHANGE_SERVICES_OF_INTEREST",
        payload: { field: "interest", value: "LTCAT", checked: true },
      });
    });
  });
});
