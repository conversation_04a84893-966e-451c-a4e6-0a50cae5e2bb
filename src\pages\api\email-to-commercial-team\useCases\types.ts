enum WorkModel {
  "in-person" = "Presencial",
  remote = "Remoto",
  hybrid = "Híbrido",
}

enum ServicesOfInterest {
  "occupational-health" = "Saúde Ocupacional",
  eSocial = "eSocial",
  LTCAT = "LTCAT",
  CIPA = "CIPA",
  SESMT = "SESMT",
}

type Input = {
  recipient: string | string[];
  variables: {
    name: string;
    role: string;
    email: string;
    phone: string;
    cnpj: string;
    numberOfCollaborators: number;
    averageMonthlyAdmissions: number;
    averageMonthlyLayoffs: number;
    workModel: keyof typeof WorkModel | "-";
    servicesOfInterest: string[];
    message: string;
  };
};

export { WorkModel, ServicesOfInterest };
export type { Input };
