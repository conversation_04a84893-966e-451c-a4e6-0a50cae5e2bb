import { Symbol } from "@onyma-ds/react";

import { Portal } from "..";

import { pngPaths } from "@/assets/paths/pngPaths";

import { Props } from "./types";

import styles from "./styles.module.scss";

export default function ModalAIBase({ show, onClose, children }: Props) {
  if (!show) return <></>;
  return (
    <Portal wrapperId="modal-know-ai">
      <div
        className={styles.modalContainer}
        role="dialog"
        aria-modal="true"
        aria-hidden={!show}
        aria-labelledby="modal-company-infos"
      >
        <div className={styles.overlay} onClick={onClose} />
        <div
          className={styles.modalContent}
          style={{ backgroundImage: pngPaths.ai.backgroundImage.path }}
        >
          <button type="button" title="Fechar" onClick={onClose}>
            <Symbol name="close" size={16} />
          </button>
          {children}
        </div>
      </div>
    </Portal>
  );
}
