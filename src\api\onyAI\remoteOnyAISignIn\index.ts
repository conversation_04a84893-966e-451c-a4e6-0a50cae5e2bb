import axios from "axios";
import { RemoteOnyAISignIn } from "./types";
import { AppError } from "@/utils/errors";

export default async function remoteOnyAISignIn({
  email,
}: RemoteOnyAISignIn.Params): Promise<RemoteOnyAISignIn.Result> {
  try {
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_API_URL}/geraldo/auth/login`,
      {
        email,
      }
    );
    const { token, questionsLeft } = response.data;
    if (!token) {
      throw new Error("Não foi possível fazer login");
    }
    return { token, questionsLeft };
  } catch (error) {
    const message =
      error.response?.data?.error ||
      error.response?.data?.message ||
      error.message;
    throw new AppError(message, error.response?.status);
  }
}
